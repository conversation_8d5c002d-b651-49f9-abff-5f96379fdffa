<div ng-controller="PickPath" class="page">
    <style>
        /* Status color coding */
        .pending { background-color: #ffe188; }
        .active { background-color: #b4dba4; }
        .inactive { background-color: #f0908c; }

        /* Status cell styling */
        .status-cell {
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 500;
            min-width: 100px;
            display: inline-block;
        }
    </style>

    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="PickPathList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                <i ng-click="PickPathList = !PickPathList" class="material-icons md-primary" ng-show="PickPathList">keyboard_arrow_up</i>
                                <i ng-click="PickPathList = !PickPathList" class="material-icons md-primary" ng-show="!PickPathList">keyboard_arrow_down</i>
                                <span ng-click="PickPathList = !PickPathList">Pick Path</span>
                                <div flex></div>
                            </div>
                        </md-toolbar>

                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                            <p>No Pick Path data available</p>
                        </div>

                        <div class="row" ng-show="PickPathList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">
                                                    <th style="width: 150px;">
                                                        <div>
                                                            Pick Login
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('PickPathName')" ng-class="{'orderby' : OrderBy == 'PickPathName'}">
                                                        <div>
                                                            Pick Path <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PickPathName'"></i>
                                                            <span ng-show="OrderBy == 'PickPathName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">
                                                        <div>
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>
                                                            <span ng-show="OrderBy == 'parttype'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('mpn')" ng-class="{'orderby' : OrderBy == 'mpn'}">
                                                        <div>
                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'mpn'"></i>
                                                            <span ng-show="OrderBy == 'mpn'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('PickQuantity')" ng-class="{'orderby' : OrderBy == 'PickQuantity'}">
                                                        <div>
                                                            Pick Progress <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PickQuantity'"></i>
                                                            <span ng-show="OrderBy == 'PickQuantity'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FromDisposition')" ng-class="{'orderby' : OrderBy == 'FromDisposition'}">
                                                        <div>
                                                            Pick From Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FromDisposition'"></i>
                                                            <span ng-show="OrderBy == 'FromDisposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ToDisposition')" ng-class="{'orderby' : OrderBy == 'ToDisposition'}">
                                                        <div>
                                                            Assign Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ToDisposition'"></i>
                                                            <span ng-show="OrderBy == 'ToDisposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">
                                                        <div>
                                                            Pick Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                            <span ng-show="OrderBy == 'CreatedDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                        <div>
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                            <span ng-show="OrderBy == 'Status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                </tr>

                                                <!-- Search/Filter Row -->
                                                <tr class="errornone">
                                                    <td>
                                                        <!-- No filter for Pick Login column -->
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PickPathName" ng-model="filter_text[0].PickPathName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="mpn" ng-model="filter_text[0].mpn" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PickProgress" ng-model="filter_text[0].PickProgress" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FromDisposition" ng-model="filter_text[0].FromDisposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ToDisposition" ng-model="filter_text[0].ToDisposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="detail in pagedItems">
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 6px; flex-wrap: wrap;">
                                                            <!-- Assign Button - Just Edit Icon -->
                                                            <md-button class="md-icon-button md-primary"
                                                                       ng-click="openUserAssignmentModal(detail)"
                                                                       aria-label="Assign Users"
                                                                       style="width: 28px; height: 28px; min-width: 28px;">
                                                                <md-icon class="material-icons" style="font-size: 18px;">edit</md-icon>
                                                            </md-button>

                                                            <!-- Assigned Users Display -->
                                                            <div ng-if="detail.AssignedUsers && detail.AssignedUsers.length > 0"
                                                                 style="display: flex; flex-wrap: wrap; gap: 3px; align-items: center;">
                                                                <span ng-repeat="user in detail.AssignedUsers"
                                                                      class="user-badge"
                                                                      style="background-color: #e8f5e8;
                                                                             color: #2e7d32;
                                                                             padding: 2px 6px;
                                                                             border-radius: 12px;
                                                                             font-size: 10px;
                                                                             font-weight: 500;
                                                                             white-space: nowrap;
                                                                             border: 1px solid #c8e6c9;">
                                                                    {{user.UserName}}
                                                                </span>
                                                            </div>

                                                            <!-- No Users Assigned Message -->
                                                            <span ng-if="!detail.AssignedUsers || detail.AssignedUsers.length === 0"
                                                                  style="color: #999; font-style: italic; font-size: 11px;">
                                                                No users assigned
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>{{detail.PickPathName}}</td>
                                                    <td>{{detail.parttype}}</td>
                                                    <td>{{detail.mpn || 'N/A'}}</td>
                                                    <td>{{detail.PickCompleted || 0}} / {{detail.PickQuantity}}</td>
                                                    <td>{{detail.FromDisposition}}</td>
                                                    <td>{{detail.ToDisposition}}</td>
                                                    <td>{{detail.CreatedDate | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td>
                                                        <span class="status-cell" ng-class="{'pending': detail.Status === 'Pending', 'active': detail.Status === 'Active', 'inactive': detail.Status === 'Inactive'}">
                                                            {{detail.Status}}
                                                        </span>
                                                    </td>
                                                </tr>
                                            </tbody>

                                            <tfoot>
                                                <tr>
                                                    <td colspan="9">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)">
                                                                    <a href>{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </md-card>
                </div>
            </article>
        </div>
    </div>

<!-- User Assignment Modal Template -->
<script type="text/ng-template" id="user-assignment-modal.html">
    <md-dialog aria-label="User Assignment" style="max-width:800px; max-height: 90%; min-width: 600px;">
        <form name="userAssignmentForm" class="form-validation">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <h2>
                        <md-icon class="material-icons">person_add</md-icon>
                        Assign Users to Pick Configuration
                    </h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="cancel()">
                        <md-icon class="material-icons" aria-label="Close dialog">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content>
                <div class="md-dialog-content">
                    <!-- Configuration Info -->
                    <div class="row" ng-if="selectedDetail">
                        <div class="col-md-12">
                            <div style="background: #e3f2fd; padding: 15px; margin-bottom: 20px; border-radius: 6px; border-left: 4px solid #2196f3;">
                                <p style="margin: 0; color: #1565c0;">
                                    <strong>Configuration:</strong> {{selectedDetail.PickPathName}} - {{selectedDetail.parttype}} - {{selectedDetail.mpn || 'N/A'}}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Available Users -->
                        <div class="col-md-6">
                            <h5 style="color: #1976d2; margin-bottom: 15px;">
                                <md-icon class="material-icons" style="vertical-align: middle; margin-right: 8px;">group</md-icon>
                                Available Users
                            </h5>

                            <md-input-container class="md-block" style="margin-bottom: 10px;">
                                <label>Search users...</label>
                                <input type="text" ng-model="userSearchText">
                            </md-input-container>

                            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                                <div ng-repeat="user in availableUsers | filter:userSearchText"
                                     style="padding: 12px; margin: 8px 0; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer; background-color: #f9f9f9; transition: background-color 0.2s;"
                                     ng-click="assignUser(user)"
                                     ng-mouseover="this.style.backgroundColor='#e8f5e8'"
                                     ng-mouseleave="this.style.backgroundColor='#f9f9f9'">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <strong style="color: #333;">{{user.UserName}}</strong><br>
                                            <small style="color: #666;">{{user.FirstName}} {{user.LastName}}</small>
                                        </div>
                                        <md-icon class="material-icons" style="color: #4caf50;">add_circle</md-icon>
                                    </div>
                                </div>
                                <div ng-if="(availableUsers | filter:userSearchText).length === 0"
                                     style="text-align: center; padding: 30px; color: #999;">
                                    <md-icon class="material-icons" style="font-size: 48px; color: #ccc;">person_outline</md-icon>
                                    <p>No available users found</p>
                                </div>
                            </div>
                        </div>

                        <!-- Assigned Users -->
                        <div class="col-md-6">
                            <h5 style="color: #1976d2; margin-bottom: 15px;">
                                <md-icon class="material-icons" style="vertical-align: middle; margin-right: 8px;">assignment_ind</md-icon>
                                Assigned Users
                            </h5>

                            <div style="max-height: 350px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                                <div ng-repeat="assignment in assignedUsers"
                                     style="padding: 12px; margin: 8px 0; border: 1px solid #c8e6c9; border-radius: 4px; background-color: #e8f5e8;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <strong style="color: #2e7d32;">{{assignment.UserName}}</strong><br>
                                            <small style="color: #666;">Assigned: {{assignment.CreatedDate | date:'MM/dd/yyyy HH:mm'}}</small>
                                        </div>
                                        <md-button class="md-icon-button md-warn" ng-click="removeUser(assignment)"
                                                   aria-label="Remove user">
                                            <md-icon class="material-icons">remove_circle</md-icon>
                                        </md-button>
                                    </div>
                                </div>
                                <div ng-if="assignedUsers.length === 0"
                                     style="text-align: center; padding: 30px; color: #999;">
                                    <md-icon class="material-icons" style="font-size: 48px; color: #ccc;">assignment_ind</md-icon>
                                    <p>No users assigned yet</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row" style="justify-content: center;">
                <md-button class="md-button md-raised btn-w-md md-default" ng-click="cancel()">
                    <md-icon class="material-icons" style="margin-right: 4px;">close</md-icon>
                    Close
                </md-button>
                <md-button class="md-button md-raised btn-w-md md-primary" ng-click="closeDialog()">
                    <md-icon class="material-icons" style="margin-right: 4px;">save</md-icon>
                    Save Assignments
                </md-button>
            </md-dialog-actions>
        </form>
    </md-dialog>
</script>
</div>