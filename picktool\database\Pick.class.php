<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class PickClass extends CommonClass
{
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}

	public function PickConfigurationSave($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			// Parse configuration details if provided
			$configurationDetails = [];
			if (isset($data['ConfigurationDetails']) && is_array($data['ConfigurationDetails'])) {
				$configurationDetails = $data['ConfigurationDetails'];
			}

			// Validate that FromDispositionID and ToDispositionID are different
			if (isset($data['FromDispositionID']) && isset($data['ToDispositionID']) &&
				!empty($data['FromDispositionID']) && !empty($data['ToDispositionID']) &&
				$data['FromDispositionID'] == $data['ToDispositionID']) {
				$json['Success'] = false;
				$json['Result'] = 'Pick from disposition and Assign disposition must be different';
				return json_encode($json);
			}

			if ($data['ConfigurationID'] == '' || !isset($data['ConfigurationID'])) { // If New Configuration
				// Check if Pick Path Name already exists for this facility
				$query = "SELECT COUNT(*) as count FROM pick_configuration WHERE PickPathName = '" . mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "'";
				$q = mysqli_query($this->connectionlink, $query);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$row = mysqli_fetch_assoc($q);
				if ($row['count'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Pick Path Name already exists for this facility';
					return json_encode($json);
				}

				// Insert new configuration
				$sql = "INSERT INTO pick_configuration (FacilityID, PickPathName, parttypeid, FromDispositionID, ToDispositionID, CreatedDate, CreatedBy,`Status`) VALUES ('" .
					mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['FromDispositionID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['ToDispositionID']) . "', NOW(), '" .
					$_SESSION['user']['UserId'] . "','Pending')";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$insert_id = mysqli_insert_id($this->connectionlink);

				
				// Build detailed action text with Part Type and dispositions
				$actionText = 'Pick Configuration Created: ' . $data['PickPathName'];

				// Get Part Type name
				if (!empty($data['parttypeid'])) {
					$partTypeQuery = "SELECT parttype FROM parttype WHERE parttypeid = '" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "'";
					$partTypeResult = mysqli_query($this->connectionlink, $partTypeQuery);
					if ($partTypeResult && mysqli_num_rows($partTypeResult) > 0) {
						$partTypeRow = mysqli_fetch_assoc($partTypeResult);
						$actionText .= ' | Part Type: ' . $partTypeRow['parttype'];
					}
				}

				// Get From Disposition name
				if (!empty($data['FromDispositionID'])) {
					$fromDispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $data['FromDispositionID']) . "'";
					$fromDispResult = mysqli_query($this->connectionlink, $fromDispQuery);
					if ($fromDispResult && mysqli_num_rows($fromDispResult) > 0) {
						$fromDispRow = mysqli_fetch_assoc($fromDispResult);
						$actionText .= ' | From Disposition: ' . $fromDispRow['disposition'];
					}
				}

				// Get To Disposition name
				if (!empty($data['ToDispositionID'])) {
					$toDispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $data['ToDispositionID']) . "'";
					$toDispResult = mysqli_query($this->connectionlink, $toDispQuery);
					if ($toDispResult && mysqli_num_rows($toDispResult) > 0) {
						$toDispRow = mysqli_fetch_assoc($toDispResult);
						$actionText .= ' | To Disposition: ' . $toDispRow['disposition'];
					}
				}

				// Add tracking record
				$this->AddPickConfigurationTracking($insert_id, $actionText, 'Pick Configuration');

				$json['Success'] = true;
				$json['Result'] = "New Pick Configuration Created";
				$json['ConfigurationID'] = $insert_id;
			} else {
				// Check if Pick Path Name already exists for this facility (excluding current record)
				$query = "SELECT COUNT(*) as count FROM pick_configuration WHERE PickPathName = '" . mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "' AND ConfigurationID != '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";
				$q = mysqli_query($this->connectionlink, $query);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$row = mysqli_fetch_assoc($q);
				if ($row['count'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Pick Path Name already exists for this facility';
					return json_encode($json);
				}

				// Update existing configuration
				$sql = "UPDATE pick_configuration SET FacilityID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "', PickPathName = '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickPathName']) . "', parttypeid = '" .
					mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "', FromDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['FromDispositionID']) . "', ToDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ToDispositionID']) . "', UpdatedDate = NOW(), UpdatedBy = '" .
					$_SESSION['user']['UserId'] . "' WHERE ConfigurationID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}								

				// Build detailed action text with Part Type and dispositions
				$actionText = 'Pick Configuration Updated: ' . $data['PickPathName'];

				// Get Part Type name
				if (!empty($data['parttypeid'])) {
					$partTypeQuery = "SELECT parttype FROM parttype WHERE parttypeid = '" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "'";
					$partTypeResult = mysqli_query($this->connectionlink, $partTypeQuery);
					if ($partTypeResult && mysqli_num_rows($partTypeResult) > 0) {
						$partTypeRow = mysqli_fetch_assoc($partTypeResult);
						$actionText .= ' | Part Type: ' . $partTypeRow['parttype'];
					}
				}

				// Get From Disposition name
				if (!empty($data['FromDispositionID'])) {
					$fromDispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $data['FromDispositionID']) . "'";
					$fromDispResult = mysqli_query($this->connectionlink, $fromDispQuery);
					if ($fromDispResult && mysqli_num_rows($fromDispResult) > 0) {
						$fromDispRow = mysqli_fetch_assoc($fromDispResult);
						$actionText .= ' | From Disposition: ' . $fromDispRow['disposition'];
					}
				}

				// Get To Disposition name
				if (!empty($data['ToDispositionID'])) {
					$toDispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $data['ToDispositionID']) . "'";
					$toDispResult = mysqli_query($this->connectionlink, $toDispQuery);
					if ($toDispResult && mysqli_num_rows($toDispResult) > 0) {
						$toDispRow = mysqli_fetch_assoc($toDispResult);
						$actionText .= ' | To Disposition: ' . $toDispRow['disposition'];
					}
				}

				// Add tracking record
				$this->AddPickConfigurationTracking($data['ConfigurationID'], $actionText, 'Pick Configuration');

				$json['Success'] = true;
				$json['Result'] = "Pick Configuration Modified";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPickConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			$query = "SELECT ConfigurationID, FacilityID, PickPathName, parttypeid, FromDispositionID, ToDispositionID FROM pick_configuration WHERE ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				// Get configuration details
				$detailsQuery = "SELECT DetailID, parttypeid, FromDispositionID, ToDispositionID, mpn, PickQuantity, PickCompleted, Status FROM pick_configuration_details WHERE ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' AND Status IN ('Pending', 'In Progress') ORDER BY CreatedDate DESC";
				$detailsResult = mysqli_query($this->connectionlink, $detailsQuery);
				$details = [];
				if ($detailsResult && mysqli_num_rows($detailsResult) > 0) {
					while ($detailRow = mysqli_fetch_assoc($detailsResult)) {
						// Convert PickQuantity to integer for proper form binding
						$detailRow['PickQuantity'] = (int)$detailRow['PickQuantity'];
						// Convert PickCompleted to integer, default to 0 if null
						$detailRow['PickCompleted'] = (int)($detailRow['PickCompleted'] ?? 0);
						$details[] = $detailRow;
					}
				}

				$json['Success'] = true;
				$json['Result'] = $row;
				$json['Details'] = $details;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Pick Configuration";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPartTypes($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT parttypeid, parttype FROM parttype WHERE Status = '1' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "' ORDER BY parttype";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result = array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Part Types Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPickEligibleDispositions($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT disposition_id, disposition FROM disposition WHERE status = 'Active' AND PickPathEligible = 1 ORDER BY disposition";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result = array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Pick Eligible Dispositions Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAllDispositions($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			$query = "SELECT disposition_id, disposition FROM disposition WHERE status = 'Active' ORDER BY disposition";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result = array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Dispositions Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function SaveConfigurationDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			// Get configuration details to include part type and dispositions
			$configQuery = "SELECT pc.parttypeid, pc.FromDispositionID, pc.ToDispositionID, pt.parttype FROM pick_configuration pc
							LEFT JOIN parttype pt ON pc.parttypeid = pt.parttypeid
							WHERE pc.ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "'";
			$configResult = mysqli_query($this->connectionlink, $configQuery);

			if (mysqli_num_rows($configResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Invalid Configuration ID";
				return json_encode($json);
			}

			$configRow = mysqli_fetch_assoc($configResult);

			// Validate MPN if provided
			if (!empty($data['mpn'])) {
				// Check if MPN exists in catalog_creation table for current facility
				$mpnQuery = "SELECT mpn_id, part_type FROM catlog_creation WHERE mpn_id = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "' AND FacilityID = '" .
					mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
				$mpnResult = mysqli_query($this->connectionlink, $mpnQuery);

				if (mysqli_num_rows($mpnResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = "Invalid MPN. MPN does not exist in the catalog for this facility.";
					return json_encode($json);
				}

				$mpnRow = mysqli_fetch_assoc($mpnResult);

				// Check if part type matches
				if (strtolower(trim($mpnRow['part_type'])) !== strtolower(trim($configRow['parttype']))) {
					$json['Success'] = false;
					$json['Result'] = "Part Type of the MPN is not matching with the Configuration Part Type. Expected: " . $configRow['parttype'] . ", Found: " . $mpnRow['part_type'];
					return json_encode($json);
				}

				// Check for duplicate MPN in the same configuration (excluding current detail if editing)
				$duplicateQuery = "SELECT DetailID FROM pick_configuration_details WHERE ConfigurationID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' AND mpn = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "' AND Status IN ('Pending', 'In Progress')";

				if (!empty($data['DetailID'])) {
					$duplicateQuery .= " AND DetailID != '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
				}

				$duplicateResult = mysqli_query($this->connectionlink, $duplicateQuery);

				if (mysqli_num_rows($duplicateResult) > 0) {
					$json['Success'] = false;
					$json['Result'] = "Duplicate MPN. This MPN is already configured for this configuration.";
					return json_encode($json);
				}
			}

			if ($data['DetailID'] == '' || !isset($data['DetailID'])) { // If New Detail
				// Insert new detail with part type and dispositions
				$sql = "INSERT INTO pick_configuration_details (ConfigurationID, parttypeid, FromDispositionID, ToDispositionID, mpn, PickQuantity, PickCompleted, CreatedDate, CreatedBy, Status) VALUES ('" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['parttypeid']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['FromDispositionID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['ToDispositionID']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "', '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickQuantity']) . "', 0, NOW(), '" .
					$_SESSION['user']['UserId'] . "', 'Pending')";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$insert_id = mysqli_insert_id($this->connectionlink);

				// Add tracking record
				$this->AddPickConfigurationTracking($data['ConfigurationID'], 'Configuration Detail Created: MPN ' . $data['mpn'], 'Pick Configuration Detail', $insert_id);

				$json['Success'] = true;
				$json['Result'] = "Configuration Detail Created";
				$json['DetailID'] = $insert_id;
			} else { // If Existing Detail
				// Check if detail exists and is in Pending status
				$checkSql = "SELECT Status FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
				$checkResult = mysqli_query($this->connectionlink, $checkSql);

				if (mysqli_num_rows($checkResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = "Invalid Detail ID";
					return json_encode($json);
				}

				$row = mysqli_fetch_assoc($checkResult);
				if ($row['Status'] !== 'Pending') {
					$json['Success'] = false;
					$json['Result'] = "Cannot edit detail. Only Pending details can be modified.";
					return json_encode($json);
				}

				// Update existing detail with part type and dispositions
				$sql = "UPDATE pick_configuration_details SET parttypeid = '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['parttypeid']) . "', FromDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['FromDispositionID']) . "', ToDispositionID = '" .
					mysqli_real_escape_string($this->connectionlink, $configRow['ToDispositionID']) . "', mpn = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "', PickQuantity = '" .
					mysqli_real_escape_string($this->connectionlink, $data['PickQuantity']) . "', UpdatedDate = NOW(), UpdatedBy = '" .
					$_SESSION['user']['UserId'] . "' WHERE DetailID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";

				$q = mysqli_query($this->connectionlink, $sql);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Add tracking record
				$this->AddPickConfigurationTracking($data['ConfigurationID'], 'Configuration Detail Updated: MPN ' . $data['mpn'], 'Pick Configuration Detail', $data['DetailID']);

				$json['Success'] = true;
				$json['Result'] = "Configuration Detail Updated";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteConfigurationDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pick Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Pick Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Pick Configuration Page';
			return json_encode($json);
		}

		try {
			// Check if detail exists and is in Pending status
			$checkSql = "SELECT ConfigurationID, mpn, Status FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
			$checkResult = mysqli_query($this->connectionlink, $checkSql);

			if (mysqli_num_rows($checkResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Invalid Detail ID";
				return json_encode($json);
			}

			$row = mysqli_fetch_assoc($checkResult);
			if ($row['Status'] !== 'Pending') {
				$json['Success'] = false;
				$json['Result'] = "Cannot delete detail. Only Pending details can be deleted.";
				return json_encode($json);
			}

			// Delete the detail
			$sql = "DELETE FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
			$q = mysqli_query($this->connectionlink, $sql);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Add tracking record
			$this->AddPickConfigurationTracking($row['ConfigurationID'], 'Configuration Detail Deleted: MPN ' . $row['mpn'], 'Pick Configuration Detail', $data['DetailID']);

			$json['Success'] = true;
			$json['Result'] = "Configuration Detail Deleted";
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateMPN($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		try {
			// Check if MPN exists in catalog_creation table for current facility
			$mpnQuery = "SELECT cc.mpn_id, cc.part_type, pt.parttype FROM catlog_creation cc
						LEFT JOIN parttype pt ON pt.parttypeid = '" . mysqli_real_escape_string($this->connectionlink, $data['parttypeid']) . "'
						WHERE cc.mpn_id = '" . mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "'
						AND cc.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID']) . "'";
			$mpnResult = mysqli_query($this->connectionlink, $mpnQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($mpnResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Invalid MPN. MPN does not exist in the catalog for this facility.";
				return json_encode($json);
			}

			$mpnRow = mysqli_fetch_assoc($mpnResult);

			// Check if part type matches
			if (strtolower(trim($mpnRow['part_type'])) !== strtolower(trim($mpnRow['parttype']))) {
				$json['Success'] = false;
				$json['Result'] = "Part Type of the MPN is not matching with the Configuration Part Type. Expected: " . $mpnRow['parttype'] . ", Found: " . $mpnRow['part_type'];
				return json_encode($json);
			}

			// Check for duplicate MPN in the same configuration
			if (!empty($data['ConfigurationID'])) {
				$duplicateQuery = "SELECT DetailID FROM pick_configuration_details WHERE ConfigurationID = '" .
					mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' AND mpn = '" .
					mysqli_real_escape_string($this->connectionlink, $data['mpn']) . "' AND Status IN ('Pending', 'In Progress')";

				if (!empty($data['DetailID'])) {
					$duplicateQuery .= " AND DetailID != '" . mysqli_real_escape_string($this->connectionlink, $data['DetailID']) . "'";
				}

				$duplicateResult = mysqli_query($this->connectionlink, $duplicateQuery);

				if (mysqli_num_rows($duplicateResult) > 0) {
					$json['Success'] = false;
					$json['Result'] = "Duplicate MPN. This MPN is already configured for this configuration.";
					return json_encode($json);
				}
			}

			$json['Success'] = true;
			$json['Result'] = "MPN is valid";
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}	

	public function GetPickConfigurationDetailsList($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array(),
			'Total' => 0
		);

		try {
			$facilityID = $_SESSION['user']['FacilityID'];
			$page = isset($data['page']) ? (int)$data['page'] : 0;
			$itemsPerPage = isset($data['itemsPerPage']) ? (int)$data['itemsPerPage'] : 25;
			$orderBy = isset($data['OrderBy']) ? $data['OrderBy'] : 'CreatedDate';
			$orderByType = isset($data['OrderByType']) ? $data['OrderByType'] : 'desc';
			$filter = isset($data['filter']) ? $data['filter'] : array();

			// Build the base query - include configurations without details
			$baseQuery = "FROM pick_configuration pc
						  LEFT JOIN pick_configuration_details pcd ON pc.ConfigurationID = pcd.ConfigurationID AND pcd.Status IN ('Pending', 'In Progress')
						  LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
						  LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
						  LEFT JOIN disposition td ON pcd.ToDispositionID = td.disposition_id
						  WHERE pc.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $facilityID) . "'";

			// Add filters
			if (!empty($filter)) {
				if (!empty($filter['PickPathName'])) {
					$baseQuery .= " AND pc.PickPathName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['PickPathName']) . "%'";
				}
				if (!empty($filter['parttype'])) {
					$baseQuery .= " AND pt.parttype LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['parttype']) . "%'";
				}
				if (!empty($filter['mpn'])) {
					$baseQuery .= " AND pcd.mpn LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['mpn']) . "%'";
				}
				if (!empty($filter['FromDisposition'])) {
					$baseQuery .= " AND fd.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['FromDisposition']) . "%'";
				}
				if (!empty($filter['ToDisposition'])) {
					$baseQuery .= " AND td.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['ToDisposition']) . "%'";
				}
				if (!empty($filter['Status'])) {
					$baseQuery .= " AND pcd.Status LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['Status']) . "%'";
				}
				if (!empty($filter['CreatedDate'])) {
					$baseQuery .= " AND DATE(pcd.CreatedDate) = '" . mysqli_real_escape_string($this->connectionlink, $filter['CreatedDate']) . "'";
				}
			}

			// Get total count
			$countQuery = "SELECT COUNT(*) as total " . $baseQuery;
			$countResult = mysqli_query($this->connectionlink, $countQuery);
			$totalRow = mysqli_fetch_assoc($countResult);
			$total = $totalRow['total'];

			// Build the main query with ordering and pagination
			$query = "SELECT
					  COALESCE(pcd.DetailID, 0) as DetailID,
					  pc.ConfigurationID,
					  COALESCE(pcd.mpn, 'No Details Added') as mpn,
					  COALESCE(pcd.PickQuantity, 0) as PickQuantity,
					  COALESCE(pcd.PickCompleted, 0) as PickCompleted,
					  COALESCE(pcd.Status, pc.Status) as Status,
					  COALESCE(pcd.CreatedDate, pc.CreatedDate) as CreatedDate,
					  pc.PickPathName,
					  COALESCE(pt.parttype, 'No Part Type') as parttype,
					  COALESCE(fd.disposition, 'No From Disposition') as FromDisposition,
					  COALESCE(td.disposition, 'No To Disposition') as ToDisposition " . $baseQuery;

			// Add ordering
			$validOrderColumns = array('PickPathName', 'parttype', 'mpn', 'PickQuantity', 'FromDisposition', 'ToDisposition', 'CreatedDate', 'Status');
			if (in_array($orderBy, $validOrderColumns)) {
				if ($orderBy == 'PickPathName') {
					$query .= " ORDER BY pc.PickPathName " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'parttype') {
					$query .= " ORDER BY pt.parttype " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'FromDisposition') {
					$query .= " ORDER BY fd.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'ToDisposition') {
					$query .= " ORDER BY td.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} else {
					$query .= " ORDER BY pcd." . $orderBy . " " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				}
			} else {
				$query .= " ORDER BY pcd.CreatedDate DESC";
			}

			// Add pagination
			$offset = $page * $itemsPerPage;
			$query .= " LIMIT " . $offset . ", " . $itemsPerPage;

			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$details = array();
			if ($result && mysqli_num_rows($result) > 0) {
				while ($row = mysqli_fetch_assoc($result)) {
					// Convert numeric fields to proper types
					$row['PickQuantity'] = (int)$row['PickQuantity'];
					$row['PickCompleted'] = (int)($row['PickCompleted'] ?? 0);
					$details[] = $row;
				}
			}

			$json['Success'] = true;
			$json['Result'] = $details;
			$json['Total'] = $total;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	public function ExportPickConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => $data
		);

		// Store data in session for the Excel template to use
		$_SESSION['PickConfigurationDetailsxls'] = $data;
		$json['Success'] = true;
		return json_encode($json);
	}

	public function GetPickPathList($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array(),
			'Total' => 0
		);

		try {
			$facilityID = $_SESSION['user']['FacilityID'];
			$page = isset($data['page']) ? (int)$data['page'] : 0;
			$itemsPerPage = isset($data['itemsPerPage']) ? (int)$data['itemsPerPage'] : 25;
			$orderBy = isset($data['OrderBy']) ? $data['OrderBy'] : 'CreatedDate';
			$orderByType = isset($data['OrderByType']) ? $data['OrderByType'] : 'desc';
			$filter = isset($data['filter']) ? $data['filter'] : array();

			// Build the base query with user assignments
			$baseQuery = "FROM pick_configuration_details pcd
						  LEFT JOIN pick_configuration pc ON pcd.ConfigurationID = pc.ConfigurationID
						  LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
						  LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
						  LEFT JOIN disposition td ON pcd.ToDispositionID = td.disposition_id
						  WHERE pc.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $facilityID) . "'
						  AND pcd.Status IN ('Pending', 'In Progress')";

			// Add filters
			if (!empty($filter)) {
				if (!empty($filter['PickPathName'])) {
					$baseQuery .= " AND pc.PickPathName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['PickPathName']) . "%'";
				}
				if (!empty($filter['parttype'])) {
					$baseQuery .= " AND pt.parttype LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['parttype']) . "%'";
				}
				if (!empty($filter['mpn'])) {
					$baseQuery .= " AND pcd.mpn LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['mpn']) . "%'";
				}
				if (!empty($filter['FromDisposition'])) {
					$baseQuery .= " AND fd.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['FromDisposition']) . "%'";
				}
				if (!empty($filter['ToDisposition'])) {
					$baseQuery .= " AND td.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['ToDisposition']) . "%'";
				}
				if (!empty($filter['Status'])) {
					$baseQuery .= " AND pcd.Status LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filter['Status']) . "%'";
				}
				if (!empty($filter['CreatedDate'])) {
					$baseQuery .= " AND DATE(pcd.CreatedDate) = '" . mysqli_real_escape_string($this->connectionlink, $filter['CreatedDate']) . "'";
				}
			}

			// Get total count
			$countQuery = "SELECT COUNT(*) as total " . $baseQuery;
			$countResult = mysqli_query($this->connectionlink, $countQuery);
			$totalRow = mysqli_fetch_assoc($countResult);
			$total = $totalRow['total'];

			// Build the main query with ordering and pagination
			$query = "SELECT pcd.DetailID, pcd.ConfigurationID, pcd.mpn, pcd.PickQuantity, pcd.PickCompleted, pcd.Status, pcd.CreatedDate,
					  pc.PickPathName, pt.parttype, fd.disposition as FromDisposition, td.disposition as ToDisposition " . $baseQuery;

			// Add ordering
			$validOrderColumns = array('PickPathName', 'parttype', 'mpn', 'PickQuantity', 'FromDisposition', 'ToDisposition', 'CreatedDate', 'Status');
			if (in_array($orderBy, $validOrderColumns)) {
				if ($orderBy == 'PickPathName') {
					$query .= " ORDER BY pc.PickPathName " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'parttype') {
					$query .= " ORDER BY pt.parttype " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'FromDisposition') {
					$query .= " ORDER BY fd.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} elseif ($orderBy == 'ToDisposition') {
					$query .= " ORDER BY td.disposition " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				} else {
					$query .= " ORDER BY pcd." . $orderBy . " " . ($orderByType == 'asc' ? 'ASC' : 'DESC');
				}
			} else {
				$query .= " ORDER BY pcd.CreatedDate DESC";
			}

			// Add pagination
			$offset = $page * $itemsPerPage;
			$query .= " LIMIT " . $offset . ", " . $itemsPerPage;

			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$details = array();
			if ($result && mysqli_num_rows($result) > 0) {
				while ($row = mysqli_fetch_assoc($result)) {
					// Convert numeric fields to proper types
					$row['PickQuantity'] = (int)$row['PickQuantity'];
					$row['PickCompleted'] = (int)($row['PickCompleted'] ?? 0);

					// Get assigned users for this detail
					$row['AssignedUsers'] = $this->GetAssignedUsers($row['DetailID']);

					$details[] = $row;
				}
			}

			$json['Success'] = true;
			$json['Result'] = $details;
			$json['Total'] = $total;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	private function GetAssignedUsers($detailID)
	{
		$assignedUsers = array();
		$query = "SELECT ua.AssignID, ua.UserId, ua.UserName, ua.CreatedDate
				  FROM pick_configuration_details_user_assignment ua
				  WHERE ua.DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'
				  ORDER BY ua.CreatedDate DESC";

		$result = mysqli_query($this->connectionlink, $query);
		if ($result && mysqli_num_rows($result) > 0) {
			while ($row = mysqli_fetch_assoc($result)) {
				$assignedUsers[] = $row;
			}
		}

		return $assignedUsers;
	}

	public function GetAvailableUsers($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array()
		);

		try {
			$facilityID = $_SESSION['user']['FacilityID'];
			$detailID = isset($data['DetailID']) ? $data['DetailID'] : '';

			// Get users who are not already assigned to this detail and have PickerController = 1
			$query = "SELECT u.UserId, u.UserName, u.FirstName, u.LastName
					  FROM users u
					  WHERE u.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $facilityID) . "'
					  AND u.PickerController = 1
					  AND u.Status = 1
					  AND u.UserId NOT IN (
						  SELECT ua.UserId
						  FROM pick_configuration_details_user_assignment ua
						  WHERE ua.DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'
					  )
					  ORDER BY u.UserName";

			$result = mysqli_query($this->connectionlink, $query);
			$users = array();

			if ($result && mysqli_num_rows($result) > 0) {
				while ($row = mysqli_fetch_assoc($result)) {
					$users[] = $row;
				}
			}

			$json['Success'] = true;
			$json['Result'] = $users;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	public function AssignUserToDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => ''
		);

		try {
			$detailID = isset($data['DetailID']) ? $data['DetailID'] : '';
			$userID = isset($data['UserID']) ? $data['UserID'] : '';
			$userName = isset($data['UserName']) ? $data['UserName'] : '';

			if (empty($detailID) || empty($userID)) {
				$json['Result'] = 'Detail ID and User ID are required';
				return json_encode($json);
			}

			// Check if user is already assigned
			$checkQuery = "SELECT AssignID FROM pick_configuration_details_user_assignment
						   WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'
						   AND UserId = '" . mysqli_real_escape_string($this->connectionlink, $userID) . "'";

			$checkResult = mysqli_query($this->connectionlink, $checkQuery);
			if (mysqli_num_rows($checkResult) > 0) {
				$json['Result'] = 'User is already assigned to this configuration';
				return json_encode($json);
			}

			// Insert new assignment
			$insertQuery = "INSERT INTO pick_configuration_details_user_assignment
							(DetailID, UserId, UserName, CreatedDate, CreatedBy)
							VALUES (
								'" . mysqli_real_escape_string($this->connectionlink, $detailID) . "',
								'" . mysqli_real_escape_string($this->connectionlink, $userID) . "',
								'" . mysqli_real_escape_string($this->connectionlink, $userName) . "',
								NOW(),
								'" . $_SESSION['user']['UserId'] . "'
							)";

			if (mysqli_query($this->connectionlink, $insertQuery)) {
				// Add tracking for user assignment
				$this->AddPickUserAssignmentTracking($detailID, 'User Assigned: ' . $userName, 'PickPath', $userName . ' assigned to pick configuration');

				$json['Success'] = true;
				$json['Result'] = 'User assigned successfully';
			} else {
				$json['Result'] = 'Failed to assign user: ' . mysqli_error($this->connectionlink);
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	public function RemoveUserFromDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => ''
		);

		try {
			$assignID = isset($data['AssignID']) ? $data['AssignID'] : '';

			if (empty($assignID)) {
				$json['Result'] = 'Assignment ID is required';
				return json_encode($json);
			}

			// Get assignment details for tracking before deletion
			$getAssignmentQuery = "SELECT ua.DetailID, ua.UserName
								   FROM pick_configuration_details_user_assignment ua
								   WHERE ua.AssignID = '" . mysqli_real_escape_string($this->connectionlink, $assignID) . "'";
			$assignmentResult = mysqli_query($this->connectionlink, $getAssignmentQuery);
			$assignmentData = mysqli_fetch_assoc($assignmentResult);

			// Delete assignment
			$deleteQuery = "DELETE FROM pick_configuration_details_user_assignment
							WHERE AssignID = '" . mysqli_real_escape_string($this->connectionlink, $assignID) . "'";

			if (mysqli_query($this->connectionlink, $deleteQuery)) {
				// Add tracking for user removal
				if ($assignmentData) {
					$this->AddPickUserAssignmentTracking($assignmentData['DetailID'], 'User Removed: ' . $assignmentData['UserName'], 'PickPath', $assignmentData['UserName'] . ' removed from pick configuration');
				}

				$json['Success'] = true;
				$json['Result'] = 'User removed successfully';
			} else {
				$json['Result'] = 'Failed to remove user: ' . mysqli_error($this->connectionlink);
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	public function GetDetailUserAssignments($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array()
		);

		try {
			$detailID = isset($data['DetailID']) ? $data['DetailID'] : '';

			if (empty($detailID)) {
				$json['Result'] = 'Detail ID is required';
				return json_encode($json);
			}

			$assignments = $this->GetAssignedUsers($detailID);

			$json['Success'] = true;
			$json['Result'] = $assignments;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}

	public function GetPickDetailTrackingHistory($data)
	{
		$json = array('Success' => false, 'Result' => '');

		try {
			$detailID = isset($data['DetailID']) ? $data['DetailID'] : '';
			$configurationID = isset($data['ConfigurationID']) ? $data['ConfigurationID'] : '';

			// If DetailID is 0 or empty, we only need ConfigurationID
			if (empty($detailID) || $detailID == '0') {
				if (empty($configurationID)) {
					$json['Result'] = 'Configuration ID is required when Detail ID is not available';
					return json_encode($json);
				}

				// Only get configuration-level tracking
				$query = "SELECT
							  pct.TrackID,
							  pct.ConfigurationID,
							  pct.DetailID,
							  pct.Action,
							  pct.ModuleName,
							  pct.CreatedBy as CreatedByID,
							  u.UserName as CreatedBy,
							  DATE_FORMAT(pct.CreatedDate, '%Y-%m-%d %H:%i:%s') as CreatedDate
						  FROM pick_configuration_tracking pct
						  LEFT JOIN users u ON pct.CreatedBy = u.UserId
						  WHERE pct.ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "'
						  ORDER BY pct.CreatedDate DESC";
			} else {
				// Get ConfigurationID from DetailID first
				$configQuery = "SELECT ConfigurationID FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'";
				$configResult = mysqli_query($this->connectionlink, $configQuery);
				if ($configResult && mysqli_num_rows($configResult) > 0) {
					$configRow = mysqli_fetch_assoc($configResult);
					$configurationID = $configRow['ConfigurationID'];
				}

				// Get tracking history for both the detail and its configuration
				$query = "SELECT
							  pct.TrackID,
							  pct.ConfigurationID,
							  pct.DetailID,
							  pct.Action,
							  pct.ModuleName,
							  pct.CreatedBy as CreatedByID,
							  u.UserName as CreatedBy,
							  DATE_FORMAT(pct.CreatedDate, '%Y-%m-%d %H:%i:%s') as CreatedDate
						  FROM pick_configuration_tracking pct
						  LEFT JOIN users u ON pct.CreatedBy = u.UserId
						  WHERE (pct.DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'";

				// Include configuration-level tracking if we found the ConfigurationID
				if ($configurationID) {
					$query .= " OR (pct.ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "' AND pct.DetailID IS NULL)";
				}

				$query .= ") ORDER BY pct.CreatedDate DESC";
			}

			$result = mysqli_query($this->connectionlink, $query);

			if (!$result) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$trackingHistory = array();
			while ($row = mysqli_fetch_assoc($result)) {
				$trackingHistory[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $trackingHistory;

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	private function AddPickUserAssignmentTracking($detailID, $action, $moduleName, $description = '')
	{
		try {
			$userID = $_SESSION['user']['UserId'];

			// Get ConfigurationID from DetailID
			$configurationID = null;
			if (!empty($detailID)) {
				$configQuery = "SELECT ConfigurationID FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'";
				$configResult = mysqli_query($this->connectionlink, $configQuery);
				if ($configResult && mysqli_num_rows($configResult) > 0) {
					$configRow = mysqli_fetch_assoc($configResult);
					$configurationID = $configRow['ConfigurationID'];
				}
			}

			$trackingQuery = "INSERT INTO pick_configuration_tracking
							  (ConfigurationID, DetailID, Action, ModuleName, CreatedBy, CreatedDate)
							  VALUES (
								  " . ($configurationID ? "'" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "'" : "NULL") . ",
								  " . ($detailID ? "'" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'" : "NULL") . ",
								  '" . mysqli_real_escape_string($this->connectionlink, $action) . "',
								  '" . mysqli_real_escape_string($this->connectionlink, $moduleName) . "',
								  '" . mysqli_real_escape_string($this->connectionlink, $userID) . "',
								  NOW()
							  )";

			mysqli_query($this->connectionlink, $trackingQuery);
		} catch (Exception $e) {
			// Log error but don't fail the main operation
			error_log("Tracking error: " . $e->getMessage());
		}
	}

	private function AddPickConfigurationTracking($configurationID, $action, $moduleName, $detailID = null)
	{
		try {
			$userID = $_SESSION['user']['UserId'];

			$sql = "INSERT INTO pick_configuration_tracking (ConfigurationID, DetailID, Action, CreatedDate, CreatedBy, ModuleName) VALUES ('" .
				mysqli_real_escape_string($this->connectionlink, $configurationID) . "', " .
				($detailID ? "'" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'" : "NULL") . ", '" .
				mysqli_real_escape_string($this->connectionlink, $action) . "', NOW(), '" .
				mysqli_real_escape_string($this->connectionlink, $userID) . "', '" .
				mysqli_real_escape_string($this->connectionlink, $moduleName) . "')";

			mysqli_query($this->connectionlink, $sql);
		} catch (Exception $e) {
			// Log error but don't fail the main operation
			error_log("Pick Configuration Tracking Error: " . $e->getMessage());
		}
	}

	// Get configurations for a specific facility (for ProcessPickPath)
	public function GetConfigurationsForFacility($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array()
		);

		try {
			$facilityID = isset($data['FacilityID']) ? $data['FacilityID'] : '';

			if (empty($facilityID)) {
				$json['Result'] = 'Facility ID is required';
				return json_encode($json);
			}

			$query = "SELECT
						pc.ConfigurationID,
						pc.FacilityID,
						pc.PickPathName,
						DATE_FORMAT(pc.CreatedDate, '%Y-%m-%d %H:%i:%s') as CreatedDate,
						pc.Status
					  FROM pick_configuration pc
					  WHERE pc.FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $facilityID) . "'
					  AND pc.Status IN ('Pending', 'In Progress')
					  ORDER BY pc.CreatedDate DESC";

			$result = mysqli_query($this->connectionlink, $query);

			if (!$result) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$configurations = array();
			while ($row = mysqli_fetch_assoc($result)) {
				$configurations[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $configurations;

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	// Get configuration details assigned to current user (for ProcessPickPath)
	public function GetUserAssignedConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array()
		);

		try {
			$configurationID = isset($data['ConfigurationID']) ? $data['ConfigurationID'] : '';
			$currentUserID = $_SESSION['user']['UserId'];

			if (empty($configurationID)) {
				$json['Result'] = 'Configuration ID is required';
				return json_encode($json);
			}

			// Get configuration details that are assigned to the current user with mapping information
			$query = "SELECT DISTINCT
						pcd.DetailID,
						pcd.ConfigurationID,
						pcd.parttypeid,
						pt.parttype,
						pcd.mpn,
						pcd.FromDispositionID,
						fd.disposition as FromDisposition,
						pcd.ToDispositionID,
						td.disposition as ToDisposition,
						pcd.PickQuantity,
						pcd.PickCompleted,
						pcd.Status,
						DATE_FORMAT(pcd.CreatedDate, '%Y-%m-%d %H:%i:%s') as CreatedDate,
						pcm.MappingID,
						pcm.CustomPalletID,
						pcm.BinName as MappedBinName,
						cp.AssetsCount,
						cp.MaximumAssets
					  FROM pick_configuration_details pcd
					  INNER JOIN pick_configuration_details_user_assignment ua ON pcd.DetailID = ua.DetailID
					  LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
					  LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
					  LEFT JOIN disposition td ON pcd.ToDispositionID = td.disposition_id
					  LEFT JOIN pick_configuration_details_custompallet_mapping pcm ON pcd.DetailID = pcm.DetailID AND pcm.UserID = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
					  LEFT JOIN custompallet cp ON pcm.CustomPalletID = cp.CustomPalletID
					  WHERE pcd.ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "'
					  AND ua.UserId = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
					  AND pcd.Status IN ('Pending', 'In Progress')
					  ORDER BY pcd.CreatedDate DESC";

			$result = mysqli_query($this->connectionlink, $query);

			if (!$result) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$details = array();
			while ($row = mysqli_fetch_assoc($result)) {
				$details[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $details;

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	// Map bin to configuration detail (for ProcessPickPath)
	public function MapBinToConfigurationDetail($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => ''
		);

		try {
			$detailID = isset($data['DetailID']) ? $data['DetailID'] : '';
			$binName = isset($data['BinName']) ? trim($data['BinName']) : '';

			// Validate session data
			if (!isset($_SESSION['user']['UserId'])) {
				$json['Result'] = 'User session invalid - UserId not found';
				return json_encode($json);
			}

			if (!isset($_SESSION['user']['FacilityID'])) {
				$json['Result'] = 'User session invalid - FacilityID not found';
				return json_encode($json);
			}

			$currentUserID = $_SESSION['user']['UserId'];

			if (empty($detailID) || empty($binName)) {
				$json['Result'] = 'Detail ID and Bin Name are required';
				return json_encode($json);
			}

			// First, verify that the current user is assigned to this detail
			$assignmentQuery = "SELECT AssignID FROM pick_configuration_details_user_assignment
							   WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'
							   AND UserId = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'";

			$assignmentResult = mysqli_query($this->connectionlink, $assignmentQuery);
			if (!$assignmentResult) {
				$json['Result'] = 'Database error in assignment check: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_num_rows($assignmentResult) == 0) {
				$json['Result'] = 'You are not assigned to this configuration detail';
				return json_encode($json);
			}

			// Get bin details with all required validations
			$binQuery = "SELECT CustomPalletID, StatusID, FacilityID, disposition_id FROM custompallet WHERE BinName = '" . mysqli_real_escape_string($this->connectionlink, $binName) . "'";
			$binResult = mysqli_query($this->connectionlink, $binQuery);

			if (!$binResult) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($binResult) == 0) {
				$json['Result'] = 'Bin not found: ' . $binName;
				return json_encode($json);
			}

			$binRow = mysqli_fetch_assoc($binResult);
			$customPalletID = $binRow['CustomPalletID'];
			$binStatusID = $binRow['StatusID'];
			$binFacilityID = $binRow['FacilityID'];
			$binDispositionID = $binRow['disposition_id'];

			// Validate bin status (must be Active = 1)
			if ($binStatusID != '1') {
				$json['Result'] = 'Bin status is not Active';
				return json_encode($json);
			}

			// Validate bin facility (must match current user's facility)
			$currentUserFacilityID = $_SESSION['user']['FacilityID'];
			if ($binFacilityID != $currentUserFacilityID) {
				$json['Result'] = 'Bin facility is different from your facility';
				return json_encode($json);
			}

			// Get ToDispositionID from the configuration detail
			$detailQuery = "SELECT ToDispositionID,`Status` FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'";
			$detailResult = mysqli_query($this->connectionlink, $detailQuery);

			if (!$detailResult) {
				$json['Result'] = 'Database error in detail query: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_num_rows($detailResult) == 0) {
				$json['Result'] = 'Configuration detail not found';
				return json_encode($json);
			}

			$detailRow = mysqli_fetch_assoc($detailResult);
			$toDispositionID = $detailRow['ToDispositionID'];

			if($detailRow['Status'] != 'Pending' && $detailRow['Status'] != 'In Progress') {
				$json['Result'] = 'Configuration detail is not in Pending or In Progress status';
				return json_encode($json);
			}

			// Check bin disposition
			if (empty($binDispositionID) || $binDispositionID == null) {
				// Bin has no disposition, automatically assign ToDispositionID
				$updateDispositionQuery = "UPDATE custompallet SET disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $toDispositionID) . "' WHERE CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "'";
				$updateResult = mysqli_query($this->connectionlink, $updateDispositionQuery);

				if (!$updateResult) {
					$json['Result'] = 'Failed to update bin disposition: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Get disposition name for tracking
				$dispositionNameQuery = "SELECT disposition FROM disposition WHERE disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $toDispositionID) . "'";
				$dispositionNameResult = mysqli_query($this->connectionlink, $dispositionNameQuery);
				$dispositionName = 'Unknown';
				if ($dispositionNameResult && mysqli_num_rows($dispositionNameResult) > 0) {
					$dispositionNameRow = mysqli_fetch_assoc($dispositionNameResult);
					$dispositionName = $dispositionNameRow['disposition'];
				}

				// Add tracking record for disposition assignment
				$trackingAction = "Disposition automatically assigned during pick path bin mapping - Disposition: " . $dispositionName;
				$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
								 VALUES ('" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "',
										 '" . mysqli_real_escape_string($this->connectionlink, $binName) . "',
										 '" . mysqli_real_escape_string($this->connectionlink, $trackingAction) . "',
										 NOW(),
										 '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
										 'ProcessPickPath')";
				mysqli_query($this->connectionlink, $trackingQuery);
				if (mysqli_error($this->connectionlink)) {
					// Log error but don't fail the main operation
					error_log("Error inserting custompallet tracking: " . mysqli_error($this->connectionlink));
				}

			} else if ($binDispositionID != $toDispositionID) {
				// Bin has different disposition than required
				$json['Result'] = 'Bin disposition does not match the required disposition for this configuration detail';
				return json_encode($json);
			}

			// Get current user's name for tracking
			$userNameQuery = "SELECT UserName FROM users WHERE UserId = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'";
			$userNameResult = mysqli_query($this->connectionlink, $userNameQuery);
			$userName = 'Unknown User';
			if ($userNameResult && mysqli_num_rows($userNameResult) > 0) {
				$userNameRow = mysqli_fetch_assoc($userNameResult);
				$userName = $userNameRow['UserName'];
			}

			// Get configuration ID for tracking
			$configQuery = "SELECT ConfigurationID FROM pick_configuration_details WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'";
			$configResult = mysqli_query($this->connectionlink, $configQuery);
			$configurationID = null;
			if ($configResult && mysqli_num_rows($configResult) > 0) {
				$configRow = mysqli_fetch_assoc($configResult);
				$configurationID = $configRow['ConfigurationID'];
			}

			// Check if mapping already exists for this DetailID and UserID combination
			$existingMappingQuery = "SELECT MappingID, BinName as OldBinName FROM pick_configuration_details_custompallet_mapping
									WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'
									AND UserID = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'";

			$existingResult = mysqli_query($this->connectionlink, $existingMappingQuery);
			if (!$existingResult) {
				$json['Result'] = 'Database error in existing mapping check: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$isUpdate = false;
			$oldBinName = '';

			if (mysqli_num_rows($existingResult) > 0) {
				// Update existing mapping
				$existingRow = mysqli_fetch_assoc($existingResult);
				$mappingID = $existingRow['MappingID'];
				$oldBinName = $existingRow['OldBinName'];
				$isUpdate = true;

				$updateQuery = "UPDATE pick_configuration_details_custompallet_mapping
							   SET CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "',
								   BinName = '" . mysqli_real_escape_string($this->connectionlink, $binName) . "',
								   CreatedDate = NOW(),
								   CreatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
							   WHERE MappingID = '" . mysqli_real_escape_string($this->connectionlink, $mappingID) . "'";

				$updateResult = mysqli_query($this->connectionlink, $updateQuery);

				if (!$updateResult) {
					$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$json['MappingID'] = $mappingID;
			} else {
				// Create new mapping
				$insertQuery = "INSERT INTO pick_configuration_details_custompallet_mapping
							   (DetailID, CustomPalletID, BinName, UserID, CreatedDate, CreatedBy)
							   VALUES (
								   '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "',
								   '" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "',
								   '" . mysqli_real_escape_string($this->connectionlink, $binName) . "',
								   '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
								   NOW(),
								   '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
							   )";

				$insertResult = mysqli_query($this->connectionlink, $insertQuery);

				if (!$insertResult) {
					$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$json['MappingID'] = mysqli_insert_id($this->connectionlink);
			}

			// Add tracking record to pick_configuration_tracking table
			if ($isUpdate) {
				$trackingAction = "Bin assignment changed for " . $userName . " from '" . $oldBinName . "' to '" . $binName . "'";
			} else {
				$trackingAction = "Bin '" . $binName . "' assigned to " . $userName;
			}

			$pickTrackingQuery = "INSERT INTO pick_configuration_tracking
								 (ConfigurationID, DetailID, Action, CreatedDate, CreatedBy, ModuleName)
								 VALUES (
									 " . ($configurationID ? "'" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "'" : "NULL") . ",
									 '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "',
									 '" . mysqli_real_escape_string($this->connectionlink, $trackingAction) . "',
									 NOW(),
									 '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
									 'ProcessPickPath'
								 )";

			mysqli_query($this->connectionlink, $pickTrackingQuery);

			if($detailRow['Status'] == 'Pending') {
				// Update detail status to In Progress
				$updateDetailQuery = "UPDATE pick_configuration_details SET `Status` = 'In Progress' WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $detailID) . "'";
				mysqli_query($this->connectionlink, $updateDetailQuery);
				$pickTrackingQuery = "INSERT INTO pick_configuration_tracking
				(ConfigurationID, DetailID, Action, CreatedDate, CreatedBy, ModuleName)
				VALUES (
					" . ($configurationID ? "'" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "'" : "NULL") . ",
					'" . mysqli_real_escape_string($this->connectionlink, $detailID) . "',
					'Detail status updated to In Progress',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
					'ProcessPickPath'
				)";
				mysqli_query($this->connectionlink, $pickTrackingQuery);
				if (mysqli_error($this->connectionlink)) {
					// Log error but don't fail the main operation
					error_log("Error inserting pick configuration tracking: " . mysqli_error($this->connectionlink));
				}
			}

			$json['Success'] = true;
			$json['CustomPalletID'] = $customPalletID;
			$json['Result'] = 'Bin mapped successfully';

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	// Process Serial Number for Pick Path
	public function ProcessSerialNumber($data) {
		$json = array(
			'Success' => false,
			'Result' => ''
		);

		try {
			$serialNumber = isset($data['SerialNumber']) ? trim($data['SerialNumber']) : '';
			$configurationID = isset($data['ConfigurationID']) ? $data['ConfigurationID'] : '';
			$currentUserID = $_SESSION['user']['UserId'];
			$currentFacilityID = $_SESSION['user']['FacilityID'];

			// Validate inputs
			if (empty($serialNumber)) {
				$json['Result'] = 'Serial number is required';
				return json_encode($json);
			}

			if (empty($configurationID)) {
				$json['Result'] = 'Configuration ID is required';
				return json_encode($json);
			}

			// Step 1: Check if serial exists in asset table with required validations
			$assetQuery = "SELECT
							a.AssetScanID,
							a.SerialNumber,
							a.UniversalModelNumber,
							a.part_type,
							a.disposition_id,
							a.CustomPalletID,
							a.FacilityID,
							a.StatusID,
							a.AWSCustomerID,
							a.COOID,
							cp.BinName as CurrentBinName,
							cp.ShippingID as FromShippingID,
							d.disposition as CurrentDisposition
						   FROM asset a
						   LEFT JOIN custompallet cp ON a.CustomPalletID = cp.CustomPalletID
						   LEFT JOIN disposition d ON a.disposition_id = d.disposition_id
						   WHERE a.SerialNumber = '" . mysqli_real_escape_string($this->connectionlink, $serialNumber) . "'";

			$assetResult = mysqli_query($this->connectionlink, $assetQuery);

			if (!$assetResult) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($assetResult) == 0) {
				// Step 1b: Check in speed_server_recovery table if not found in asset table
				$serverQuery = "SELECT
								s.ServerID,
								s.ServerSerialNumber as SerialNumber,
								s.MPN as UniversalModelNumber,
								s.Type as part_type,
								s.disposition_id,
								s.CustomPalletID,
								p.PalletFacilityID as FacilityID,
								s.StatusID,
								s.AWSCustomerID,
								s.COOID,
								cp.BinName as CurrentBinName,
								cp.ShippingID as FromShippingID,
								d.disposition as CurrentDisposition
							   FROM speed_server_recovery s
							   LEFT JOIN custompallet cp ON s.CustomPalletID = cp.CustomPalletID
							   LEFT JOIN pallets p ON s.idPallet = p.idPallet
							   LEFT JOIN disposition d ON s.disposition_id = d.disposition_id
							   WHERE s.ServerSerialNumber = '" . mysqli_real_escape_string($this->connectionlink, $serialNumber) . "'";

				$serverResult = mysqli_query($this->connectionlink, $serverQuery);

				if (!$serverResult) {
					$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($serverResult) == 0) {
					// Step 1c: Check in speed_media_recovery table if not found in server table
					$mediaQuery = "SELECT
									m.MediaID,
									m.MediaSerialNumber as SerialNumber,
									m.MediaMPN as UniversalModelNumber,
									m.MediaType as part_type,
									m.disposition_id,
									m.CustomPalletID,
									m.FacilityID,
									m.StatusID,
									m.AWSCustomerID,
									m.COOID,
									cp.BinName as CurrentBinName,
									cp.ShippingID as FromShippingID,
									d.disposition as CurrentDisposition
								   FROM speed_media_recovery m
								   LEFT JOIN custompallet cp ON m.CustomPalletID = cp.CustomPalletID
								   LEFT JOIN disposition d ON m.disposition_id = d.disposition_id
								   WHERE m.MediaSerialNumber = '" . mysqli_real_escape_string($this->connectionlink, $serialNumber) . "'";

					$mediaResult = mysqli_query($this->connectionlink, $mediaQuery);

					if (!$mediaResult) {
						$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					if (mysqli_num_rows($mediaResult) == 0) {
						$json['Result'] = 'Serial number not found in asset, speed_server_recovery, or speed_media_recovery tables';
						return json_encode($json);
					}

					$asset = mysqli_fetch_assoc($mediaResult);
					$asset['AssetType'] = 'Media'; // Mark as media for later processing
				} else {
					$asset = mysqli_fetch_assoc($serverResult);
					$asset['AssetType'] = 'Server'; // Mark as server for later processing
				}
			} else {
				$asset = mysqli_fetch_assoc($assetResult);
				$asset['AssetType'] = 'Asset'; // Mark as asset for later processing
			}

			// Step 2: Validate facility
			if ($asset['FacilityID'] != $currentFacilityID) {
				$json['Result'] = 'Serial does not belong to current facility';
				return json_encode($json);
			}

			// Step 3: Validate status
			if ($asset['AssetType'] == 'Media') {
				// Media records: exclude StatusID = 6 (shipped), allow all others
				if ($asset['StatusID'] == '6') {
					$json['Result'] = 'Media Status is Shipped and cannot be processed';
					return json_encode($json);
				}
			} else {
				// Asset and Server records: only allow StatusID 1, 8, 9
				if (!in_array($asset['StatusID'], ['1', '8', '9'])) {
					$json['Result'] = 'Serial status is not valid for processing';
					return json_encode($json);
				}
			}

			// Step 4: Check if MPN matches any DetailID in pick_configuration_details for this configuration
			$mpnMatchQuery = "SELECT
								pcd.DetailID,
								pcd.FromDispositionID,
								pcd.ToDispositionID,
								pcd.parttypeid,
								pcd.PickQuantity,
								pcd.PickCompleted,
								fd.disposition as FromDisposition,
								td.disposition as ToDisposition,
								pt.parttype
							  FROM pick_configuration_details pcd
							  LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
							  LEFT JOIN disposition td ON pcd.ToDispositionID = td.disposition_id
							  LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
							  WHERE pcd.ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "'
							  AND pcd.mpn = '" . mysqli_real_escape_string($this->connectionlink, $asset['UniversalModelNumber']) . "'
							  AND pcd.Status IN ('Pending', 'In Progress')";

			$mpnResult = mysqli_query($this->connectionlink, $mpnMatchQuery);

			if (!$mpnResult || mysqli_num_rows($mpnResult) == 0) {
				$json['Result'] = 'MPN not matching with any configuration detail';
				return json_encode($json);
			}

			$matchedDetail = mysqli_fetch_assoc($mpnResult);

			// Step 5: Validate pick quantity limits (PickCompleted < PickQuantity)
			$pickCompleted = intval($matchedDetail['PickCompleted']);
			$pickQuantity = intval($matchedDetail['PickQuantity']);

			if ($pickCompleted >= $pickQuantity) {
				$json['Result'] = 'Pick quantity limit reached. Completed: ' . $pickCompleted . ', Maximum: ' . $pickQuantity;
				return json_encode($json);
			}

			// Step 6: Validate serial disposition and part type match with Pick Configuration
			if ($asset['disposition_id'] != $matchedDetail['FromDispositionID']) {
				$json['Result'] = 'Serial disposition (' . $asset['CurrentDisposition'] . ') does not match Pick From Disposition (' . $matchedDetail['FromDisposition'] . ')';
				return json_encode($json);
			}

			if ($asset['part_type'] != $matchedDetail['parttype']) {
				$json['Result'] = 'Serial part type (' . $asset['part_type'] . ') does not match Pick Configuration Part Type (' . $matchedDetail['parttype'] . ')';
				return json_encode($json);
			}

			// Step 7: Check if user is assigned to this detail
			$userAssignmentQuery = "SELECT AssignID FROM pick_configuration_details_user_assignment
									WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['DetailID']) . "'
									AND UserID = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'";

			$assignmentResult = mysqli_query($this->connectionlink, $userAssignmentQuery);

			if (!$assignmentResult || mysqli_num_rows($assignmentResult) == 0) {
				$json['Result'] = 'You are not assigned to work on this configuration detail';
				return json_encode($json);
			}

			// Step 8: Get destination bin for this detail
			$destinationBinQuery = "SELECT
									pcm.CustomPalletID as DestinationCustomPalletID,
									pcm.BinName as DestinationBinName,
									cp.StatusID as DestinationBinStatus,
									cp.FacilityID as DestinationBinFacility,
									cp.disposition_id as DestinationBinDisposition,
									cp.AssetsCount,
									cp.MaximumAssets,
									cp.MaxLimitRequired,
									cp.CustomerLock,
									cp.AWSCustomerID as DestinationBinCustomerID,
									cp.ShippingID as ToShippingID
								   FROM pick_configuration_details_custompallet_mapping pcm
								   LEFT JOIN custompallet cp ON pcm.CustomPalletID = cp.CustomPalletID
								   WHERE pcm.DetailID = '" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['DetailID']) . "'
								   AND pcm.UserID = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'";

			$destinationResult = mysqli_query($this->connectionlink, $destinationBinQuery);

			if (!$destinationResult || mysqli_num_rows($destinationResult) == 0) {
				$json['Result'] = 'No destination bin mapped for this configuration detail. Please map a bin first.';
				return json_encode($json);
			}

			$destinationBin = mysqli_fetch_assoc($destinationResult);

			// Step 9: Validate destination bin
			if ($destinationBin['DestinationBinStatus'] != '1') {
				$json['Result'] = 'Destination bin is not active';
				return json_encode($json);
			}

			if ($destinationBin['DestinationBinFacility'] != $currentFacilityID) {
				$json['Result'] = 'Destination bin does not belong to current facility';
				return json_encode($json);
			}

			// Step 10: Check bin capacity (only if MaxLimitRequired = 1)
			if ($destinationBin['MaxLimitRequired'] == '1') {
				$currentAssetsInBin = intval($destinationBin['AssetsCount']);
				$maxAssetsAllowed = intval($destinationBin['MaximumAssets']);

				if (($currentAssetsInBin + 1) > $maxAssetsAllowed) {
					$json['Result'] = "Destination bin does not have enough space. Current count: $currentAssetsInBin, Maximum capacity: $maxAssetsAllowed";
					return json_encode($json);
				}
			}

			// Step 11: Validate CustomerLock for destination bin
			if ($destinationBin['CustomerLock'] == '1') {
				// Get the AWSCustomerID from the asset being processed
				$assetCustomerID = isset($asset['AWSCustomerID']) ? $asset['AWSCustomerID'] : null;

				// Use the existing ValidateCustomerLockForBin function from common_functions.php				
				$customerLockValidation = $this->ValidateCustomerLockForBin(
					$destinationBin['DestinationCustomPalletID'],
					$assetCustomerID,
					$destinationBin['DestinationBinName'],
					$currentUserID,
					'ProcessPickPath'  // Module name for tracking
				);

				if (!$customerLockValidation['Success']) {
					$json['Result'] = $customerLockValidation['Result'];
					return json_encode($json);
				}
			}

			// All validations passed - proceed with serial movement

			// Step 12: Move serial to destination bin
			$fromCustomPalletID = $asset['CustomPalletID'];
			$toCustomPalletID = $destinationBin['DestinationCustomPalletID'];
			$fromBinName = $asset['CurrentBinName'];
			$toBinName = $destinationBin['DestinationBinName'];

			// Generate event ID for tracking
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'N';

			// Step 12a: Insert into picktool_asset_movement table for tracking
			if ($asset['AssetType'] == 'Asset') {
				$movementQuery = "INSERT INTO picktool_asset_movement (
					AssetScanID, SerialNumber, FromCustomPalletID, ToCustomPalletID, FacilityID,
					UniversalModelNumber, part_type, disposition_id, CreatedDate, CreatedBy,
					MovementType, FromBinName, ToBinName, ConfigurationID, DetailID, event_id, batch_event_flag
				) VALUES (
					'" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['SerialNumber']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $currentFacilityID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['UniversalModelNumber']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['part_type']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['disposition_id']) . "',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
					'ProcessPickPath',
					'" . mysqli_real_escape_string($this->connectionlink, $fromBinName) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['DetailID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $event_id) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $batch_event_flag) . "'
				)";
			} else if ($asset['AssetType'] == 'Server') {
				$movementQuery = "INSERT INTO picktool_asset_movement (
					ServerID, SerialNumber, FromCustomPalletID, ToCustomPalletID, FacilityID,
					UniversalModelNumber, part_type, disposition_id, CreatedDate, CreatedBy,
					MovementType, FromBinName, ToBinName, ConfigurationID, DetailID, event_id, batch_event_flag
				) VALUES (
					'" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['SerialNumber']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $currentFacilityID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['UniversalModelNumber']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['part_type']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['disposition_id']) . "',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
					'ProcessPickPath',
					'" . mysqli_real_escape_string($this->connectionlink, $fromBinName) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['DetailID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $event_id) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $batch_event_flag) . "'
				)";
			} else { // Media
				$movementQuery = "INSERT INTO picktool_asset_movement (
					MediaID, SerialNumber, FromCustomPalletID, ToCustomPalletID, FacilityID,
					UniversalModelNumber, part_type, disposition_id, CreatedDate, CreatedBy,
					MovementType, FromBinName, ToBinName, ConfigurationID, DetailID, event_id, batch_event_flag
				) VALUES (
					'" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['SerialNumber']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $currentFacilityID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['UniversalModelNumber']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['part_type']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $asset['disposition_id']) . "',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "',
					'ProcessPickPath',
					'" . mysqli_real_escape_string($this->connectionlink, $fromBinName) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $configurationID) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['DetailID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $event_id) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $batch_event_flag) . "'
				)";
			}

			$movementResult = mysqli_query($this->connectionlink, $movementQuery);
			if (!$movementResult) {
				$json['Result'] = 'Error recording movement: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Step 12b: Update asset/server/media table - move serial to destination bin
			if ($asset['AssetType'] == 'Asset') {
				$updateAssetQuery = "UPDATE asset SET
					CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
					disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['ToDispositionID']) . "',
					DateUpdated = NOW(),
					UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
					WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
			} else if ($asset['AssetType'] == 'Server') {
				$updateAssetQuery = "UPDATE speed_server_recovery SET
					CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
					disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['ToDispositionID']) . "',
					UpdatedDate = NOW(),
					UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
					WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
			} else { // Media
				$updateAssetQuery = "UPDATE speed_media_recovery SET
					CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
					disposition_id = '" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['ToDispositionID']) . "',
					UpdatedDate = NOW(),
					UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
					WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
			}

			$updateAssetResult = mysqli_query($this->connectionlink, $updateAssetQuery);
			if (!$updateAssetResult) {
				$json['Result'] = 'Error updating ' . strtolower($asset['AssetType']) . ': ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Step 12c: Update custompallet_items table
			if ($asset['AssetType'] == 'Asset') {
				$updateItemsQuery = "UPDATE custompallet_items SET
					CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "'
					WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
			} else if ($asset['AssetType'] == 'Server') {
				$updateItemsQuery = "UPDATE custompallet_items SET
					CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "'
					WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
			} else { // Media
				$updateItemsQuery = "UPDATE custompallet_items SET
					CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "'
					WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
			}

			$updateItemsResult = mysqli_query($this->connectionlink, $updateItemsQuery);
			if (!$updateItemsResult) {
				$json['Result'] = 'Error updating custompallet items: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Step 12d: Update bin asset counts
			// Increment destination bin count
			$incrementDestQuery = "UPDATE custompallet SET
				AssetsCount = AssetsCount + 1,
				LastModifiedDate = NOW(),
				LastModifiedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
				WHERE CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "'";

			$incrementDestResult = mysqli_query($this->connectionlink, $incrementDestQuery);
			if (!$incrementDestResult) {
				$json['Result'] = 'Error updating destination bin count: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Decrement source bin count (only if source bin exists)
			if (!empty($fromCustomPalletID) && $fromCustomPalletID != '0') {
				$decrementSourceQuery = "UPDATE custompallet SET
					AssetsCount = AssetsCount - 1,
					LastModifiedDate = NOW(),
					LastModifiedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
					WHERE CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "'";

				$decrementSourceResult = mysqli_query($this->connectionlink, $decrementSourceQuery);
				if (!$decrementSourceResult) {
					$json['Result'] = 'Error updating source bin count: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

			// Step 12e: Insert into tracking table
			if ($asset['AssetType'] == 'Asset') {
				$trackingDesc = "Asset Moved from BIN (" . $fromBinName . ") to BIN (" . $toBinName . ") in ProcessPickPath";
				$trackingQuery = "INSERT INTO asset_tracking (
					AssetScanID, Action, Description, UniqueID, CreatedDate, CreatedBy
				) VALUES (
					'" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $trackingDesc) . "',
					'',
					'',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
				)";
			} else if ($asset['AssetType'] == 'Server') {
				$trackingDesc = "Serial Moved from BIN (" . $fromBinName . ") to BIN (" . $toBinName . ") in ProcessPickPath";
				$trackingQuery = "INSERT INTO speed_server_recovery_tracking (
					ServerID, Action, Description, CreatedDate, CreatedBy
				) VALUES (
					'" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $trackingDesc) . "',
					'',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
				)";
			} else { // Media
				$trackingDesc = "Media Moved from BIN (" . $fromBinName . ") to BIN (" . $toBinName . ") in ProcessPickPath";
				$trackingQuery = "INSERT INTO speed_media_recovery_tracking (
					MediaID, Action, Description, CreatedDate, CreatedBy
				) VALUES (
					'" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "',
					'" . mysqli_real_escape_string($this->connectionlink, $trackingDesc) . "',
					'',
					NOW(),
					'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
				)";
			}

			$trackingResult = mysqli_query($this->connectionlink, $trackingQuery);
			if (!$trackingResult) {
				$json['Result'] = 'Error adding ' . strtolower($asset['AssetType']) . ' tracking: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Step 13: Update PickCompleted count in pick_configuration_details
			$updatePickCompletedQuery = "UPDATE pick_configuration_details SET
				PickCompleted = PickCompleted + 1,
				UpdatedDate = NOW(),
				UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
				WHERE DetailID = '" . mysqli_real_escape_string($this->connectionlink, $matchedDetail['DetailID']) . "'";

			$updatePickCompletedResult = mysqli_query($this->connectionlink, $updatePickCompletedQuery);
			if (!$updatePickCompletedResult) {
				$json['Result'] = 'Error updating pick completed count: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Step 15: Manage asset status and shipping updates based on shipment status
			$fromShippingID = $asset['FromShippingID'];
			$toShippingID = $destinationBin['ToShippingID'];

			// Update asset/server/media StatusID and ShippingID based on destination bin shipment status
			if(($fromShippingID == '' || $fromShippingID == NULL) && ($toShippingID != '' && $toShippingID != NULL)) {
				// Moving from non-shipment bin to shipment bin - Update StatusID (8 for Asset/Server, 5 for Media)
				if ($asset['AssetType'] == 'Asset') {
					$updateAssetShippingQuery = "UPDATE asset SET
						StatusID = '8',
						ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $toShippingID) . "',
						DateUpdated = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
				} else if ($asset['AssetType'] == 'Server') {
					$updateAssetShippingQuery = "UPDATE speed_server_recovery SET
						StatusID = '8',
						ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $toShippingID) . "',
						UpdatedDate = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
				} else { // Media
					$updateAssetShippingQuery = "UPDATE speed_media_recovery SET
						StatusID = '5',
						ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $toShippingID) . "',
						UpdatedDate = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
				}

				$updateAssetShippingResult = mysqli_query($this->connectionlink, $updateAssetShippingQuery);
				if (!$updateAssetShippingResult) {
					$json['Result'] = 'Error updating ' . strtolower($asset['AssetType']) . ' shipping status: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else if(($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID == '' || $toShippingID == NULL)) {
				// Moving from shipment bin to non-shipment bin - Update StatusID to 1 (Active) and clear ShippingID
				if ($asset['AssetType'] == 'Asset') {
					$updateAssetShippingQuery = "UPDATE asset SET
						StatusID = '1',
						ShippingID = NULL,
						DateUpdated = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
				} else if ($asset['AssetType'] == 'Server') {
					$updateAssetShippingQuery = "UPDATE speed_server_recovery SET
						StatusID = '1',
						ShippingID = NULL,
						UpdatedDate = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
				} else { // Media
					$updateAssetShippingQuery = "UPDATE speed_media_recovery SET
						StatusID = '1',
						ShippingID = NULL,
						UpdatedDate = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
				}

				$updateAssetShippingResult = mysqli_query($this->connectionlink, $updateAssetShippingQuery);
				if (!$updateAssetShippingResult) {
					$json['Result'] = 'Error updating ' . strtolower($asset['AssetType']) . ' shipping status: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else if(($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID != '' && $toShippingID != NULL) && ($fromShippingID != $toShippingID)) {
				// Moving between different shipments - Update ShippingID to new shipment
				if ($asset['AssetType'] == 'Asset') {
					$updateAssetShippingQuery = "UPDATE asset SET
						ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $toShippingID) . "',
						DateUpdated = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
				} else if ($asset['AssetType'] == 'Server') {
					$updateAssetShippingQuery = "UPDATE speed_server_recovery SET
						ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $toShippingID) . "',
						UpdatedDate = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
				} else { // Media
					$updateAssetShippingQuery = "UPDATE speed_media_recovery SET
						ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $toShippingID) . "',
						UpdatedDate = NOW(),
						UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
						WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
				}

				$updateAssetShippingResult = mysqli_query($this->connectionlink, $updateAssetShippingQuery);
				if (!$updateAssetShippingResult) {
					$json['Result'] = 'Error updating ' . strtolower($asset['AssetType']) . ' shipping ID: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

			// Step 16: Manage shipping_container_serials table based on shipment status

			// Case 1: Moving from non-shipment bin to shipment bin - Insert into shipping_container_serials
			if(($fromShippingID == '' || $fromShippingID == NULL) && ($toShippingID != '' && $toShippingID != NULL)) {
				if ($asset['AssetType'] == 'Asset') {
					$insertShippingQuery = "INSERT INTO shipping_container_serials (
						SerialNumber, AssetScanID, Notes, StatusID, CreatedDate, CreatedBy,
						UniversalModelNumber, part_type, CustomPalletID, BinName, FromCustomPalletID, FromBinName, COO, COOID
					)
					SELECT
						a.SerialNumber,
						a.AssetScanID,
						'Added via ProcessPickPath' as Notes,
						'1' as StatusID,
						NOW() as CreatedDate,
						'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "' as CreatedBy,
						a.UniversalModelNumber,
						a.part_type,
						'" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "' as CustomPalletID,
						'" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "' as BinName,
						'" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "' as FromCustomPalletID,
						'" . mysqli_real_escape_string($this->connectionlink, $fromBinName) . "' as FromBinName,
						c.COO,
						a.COOID
					FROM asset a
					LEFT JOIN COO c ON a.COOID = c.COOID
					WHERE a.AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
				} else if ($asset['AssetType'] == 'Server') {
					$insertShippingQuery = "INSERT INTO shipping_container_serials (
						ServerID, ServerSerialNumber, Notes, StatusID, CreatedDate, CreatedBy,
						UniversalModelNumber, part_type, CustomPalletID, BinName, FromCustomPalletID, FromBinName, COO, Quantity
					)
					SELECT
						s.ServerID,
						s.ServerSerialNumber,
						'Added via ProcessPickPath' as Notes,
						'1' as StatusID,
						NOW() as CreatedDate,
						'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "' as CreatedBy,
						s.MPN,
						s.Type,
						'" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "' as CustomPalletID,
						'" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "' as BinName,
						'" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "' as FromCustomPalletID,
						'" . mysqli_real_escape_string($this->connectionlink, $fromBinName) . "' as FromBinName,
						c.COO,
						'1' as Quantity
					FROM speed_server_recovery s
					LEFT JOIN COO c ON s.COOID = c.COOID
					WHERE s.ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
				} else { // Media
					$insertShippingQuery = "INSERT INTO shipping_container_serials (
						MediaID, MediaSerialNumber, Notes, StatusID, CreatedDate, CreatedBy,
						UniversalModelNumber, part_type, CustomPalletID, BinName, FromCustomPalletID, FromBinName, COO, Quantity
					)
					SELECT
						m.MediaID,
						m.MediaSerialNumber,
						'Added via ProcessPickPath' as Notes,
						'1' as StatusID,
						NOW() as CreatedDate,
						'" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "' as CreatedBy,
						m.MediaMPN,
						m.MediaType,
						'" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "' as CustomPalletID,
						'" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "' as BinName,
						'" . mysqli_real_escape_string($this->connectionlink, $fromCustomPalletID) . "' as FromCustomPalletID,
						'" . mysqli_real_escape_string($this->connectionlink, $fromBinName) . "' as FromBinName,
						c.COO,
						'1' as Quantity
					FROM speed_media_recovery m
					LEFT JOIN COO c ON m.COOID = c.COOID
					WHERE m.MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
				}

				$insertShippingResult = mysqli_query($this->connectionlink, $insertShippingQuery);
				if (!$insertShippingResult) {
					$json['Result'] = 'Error adding ' . strtolower($asset['AssetType']) . ' to shipping_container_serials: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}
			// Case 2: Moving from shipment bin to non-shipment bin - Remove from shipping_container_serials
			else if(($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID == '' || $toShippingID == NULL)) {
				if ($asset['AssetType'] == 'Asset') {
					$deleteShippingQuery = "DELETE FROM shipping_container_serials WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
				} else if ($asset['AssetType'] == 'Server') {
					$deleteShippingQuery = "DELETE FROM shipping_container_serials WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
				} else { // Media
					$deleteShippingQuery = "DELETE FROM shipping_container_serials WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
				}
				$deleteShippingResult = mysqli_query($this->connectionlink, $deleteShippingQuery);
				if (!$deleteShippingResult) {
					$json['Result'] = 'Error removing ' . strtolower($asset['AssetType']) . ' from shipping_container_serials: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}
			// Case 3&4: Moving between shipments or within same shipment - Update shipping_container_serials
			else if(($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID != '' && $toShippingID != NULL)) {
				if ($asset['AssetType'] == 'Asset') {
					$updateShippingQuery = "UPDATE shipping_container_serials
											SET CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
												BinName = '" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "',
												UpdatedDate = NOW(),
												UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
											WHERE AssetScanID = '" . mysqli_real_escape_string($this->connectionlink, $asset['AssetScanID']) . "'";
				} else if ($asset['AssetType'] == 'Server') {
					$updateShippingQuery = "UPDATE shipping_container_serials
											SET CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
												BinName = '" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "',
												UpdatedDate = NOW(),
												UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
											WHERE ServerID = '" . mysqli_real_escape_string($this->connectionlink, $asset['ServerID']) . "'";
				} else { // Media
					$updateShippingQuery = "UPDATE shipping_container_serials
											SET CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $toCustomPalletID) . "',
												BinName = '" . mysqli_real_escape_string($this->connectionlink, $toBinName) . "',
												UpdatedDate = NOW(),
												UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $currentUserID) . "'
											WHERE MediaID = '" . mysqli_real_escape_string($this->connectionlink, $asset['MediaID']) . "'";
				}
				$updateShippingResult = mysqli_query($this->connectionlink, $updateShippingQuery);
				if (!$updateShippingResult) {
					$json['Result'] = 'Error updating ' . strtolower($asset['AssetType']) . ' in shipping_container_serials: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

			// All operations completed successfully
			$json['Success'] = true;
			$json['Result'] = 'Serial ' . $asset['SerialNumber'] . ' successfully moved from ' . $fromBinName . ' to ' . $toBinName;

			return json_encode($json);

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetRecentTransactions($data) {
		$json = array();
		$json['Success'] = false;

		try {
			$configurationID = mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']);
			$currentUserID = $_SESSION['user']['UserId'];

			// Get recent transactions for this configuration and user (last 5 transactions)
			$query = "SELECT
						pam.SerialNumber,
						pam.UniversalModelNumber,
						pam.FromBinName,
						pam.ToBinName,
						pam.CreatedDate
					FROM picktool_asset_movement pam
					WHERE pam.ConfigurationID = '$configurationID'
					AND pam.CreatedBy = '$currentUserID'
					ORDER BY pam.CreatedDate DESC
					LIMIT 5";

			$result = mysqli_query($this->connectionlink, $query);

			if (!$result) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$transactions = array();
			while ($row = mysqli_fetch_assoc($result)) {
				$transactions[] = $row;
			}

			$json['Success'] = true;
			$json['Result'] = $transactions;

			return json_encode($json);

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
			return json_encode($json);
		}
	}

	public function ProcessSourceBin($data) {
		$json = array();
		$json['Success'] = false;

		try {
			$sourceBinName = mysqli_real_escape_string($this->connectionlink, $data['SourceBinName']);
			$configurationID = mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']);
			$currentUserID = $_SESSION['user']['UserId'];
			$currentFacilityID = $_SESSION['user']['FacilityID'];

			// Step 1: Validate source bin exists and get bin details
			$sourceBinQuery = "SELECT cp.*, d.disposition as CurrentDisposition
							   FROM custompallet cp
							   LEFT JOIN disposition d ON cp.disposition_id = d.disposition_id
							   WHERE cp.BinName = '$sourceBinName'
							   AND cp.FacilityID = '$currentFacilityID'";

			$sourceBinResult = mysqli_query($this->connectionlink, $sourceBinQuery);

			if (!$sourceBinResult) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($sourceBinResult) == 0) {
				$json['Result'] = 'Source bin not found in your facility';
				return json_encode($json);
			}

			$sourceBin = mysqli_fetch_assoc($sourceBinResult);

			// Step 2: Validate source bin status
			if ($sourceBin['StatusID'] == 7) {
				$json['Result'] = 'Source bin is shipped and cannot be processed';
				return json_encode($json);
			}

			if ($sourceBin['StatusID'] == 5) {
				$json['Result'] = 'Source bin is deleted and cannot be processed';
				return json_encode($json);
			}

			// Step 3: Check if source bin is closed
			if ($sourceBin['SealID'] != '' && $sourceBin['SealID'] != NULL) {
				$json['Result'] = 'Source bin is closed and cannot be processed';
				return json_encode($json);
			}

			// Step 4: Check if source bin has assets
			if ($sourceBin['AssetsCount'] == 0) {
				$json['Result'] = 'Source bin has no serials to process';
				return json_encode($json);
			}

			// Step 5: Get all serials from source bin across all tables
			$allSerials = array();

			// Get assets
			$assetQuery = "SELECT a.AssetScanID as ID, a.SerialNumber, a.UniversalModelNumber, a.part_type,
							a.disposition_id, a.AWSCustomerID, a.COOID, a.StatusID, a.parttypeid, 'Asset' as AssetType,
							d.disposition as DispositionName
						   FROM asset a
						   LEFT JOIN disposition d ON a.disposition_id = d.disposition_id
						   WHERE a.CustomPalletID = '{$sourceBin['CustomPalletID']}'";
			$assetResult = mysqli_query($this->connectionlink, $assetQuery);
			if ($assetResult) {
				while ($row = mysqli_fetch_assoc($assetResult)) {
					$allSerials[] = $row;
				}
			}

			// Get servers
			$serverQuery = "SELECT s.ServerID as ID, s.ServerSerialNumber as SerialNumber,
							s.MPN as UniversalModelNumber, s.Type as part_type,
							s.disposition_id, s.AWSCustomerID, s.COOID, s.StatusID, s.parttypeid, 'Server' as AssetType,
							d.disposition as DispositionName
						   FROM speed_server_recovery s
						   LEFT JOIN disposition d ON s.disposition_id = d.disposition_id
						   WHERE s.CustomPalletID = '{$sourceBin['CustomPalletID']}'";
			$serverResult = mysqli_query($this->connectionlink, $serverQuery);
			if ($serverResult) {
				while ($row = mysqli_fetch_assoc($serverResult)) {
					$allSerials[] = $row;
				}
			}

			// Get media
			$mediaQuery = "SELECT m.MediaID as ID, m.MediaSerialNumber as SerialNumber,
						   m.MediaMPN as UniversalModelNumber, m.MediaType as part_type,
						   m.disposition_id, m.AWSCustomerID, m.COOID, m.StatusID, m.parttypeid, 'Media' as AssetType,
						   d.disposition as DispositionName
						   FROM speed_media_recovery m
						   LEFT JOIN disposition d ON m.disposition_id = d.disposition_id
						   WHERE m.CustomPalletID = '{$sourceBin['CustomPalletID']}'";
			$mediaResult = mysqli_query($this->connectionlink, $mediaQuery);
			if ($mediaResult) {
				while ($row = mysqli_fetch_assoc($mediaResult)) {
					$allSerials[] = $row;
				}
			}

			if (empty($allSerials)) {
				$json['Result'] = 'No serials found in source bin';
				return json_encode($json);
			}

			// Step 6: Validate all MPNs are the same
			$mpns = array_unique(array_column($allSerials, 'UniversalModelNumber'));
			$mpns = array_filter($mpns); // Remove empty values
			if (count($mpns) > 1) {
				$json['Result'] = 'Source bin contains multiple MPNs (' . implode(', ', $mpns) . '). All MPNs must be the same for bulk processing';
				return json_encode($json);
			}

			// Step 7: Validate all PartTypeIDs are the same
			$partTypeIDs = array_unique(array_column($allSerials, 'parttypeid'));
			$partTypeIDs = array_filter($partTypeIDs); // Remove empty values
			if (count($partTypeIDs) > 1) {
				$partTypeNames = array_unique(array_column($allSerials, 'part_type'));
				$json['Result'] = 'Source bin contains multiple Part Types (' . implode(', ', $partTypeNames) . '). All Part Types must be the same for bulk processing';
				return json_encode($json);
			}

			// Step 8: Validate all dispositions are the same
			$dispositions = array_unique(array_column($allSerials, 'disposition_id'));
			$dispositions = array_filter($dispositions); // Remove empty values
			if (count($dispositions) > 1) {
				$dispositionNames = array_unique(array_column($allSerials, 'DispositionName'));
				$json['Result'] = 'Source bin contains multiple dispositions (' . implode(', ', $dispositionNames) . '). All dispositions must be the same for bulk processing';
				return json_encode($json);
			}

			// Get the common values for matching
			$commonMPN = !empty($mpns) ? reset($mpns) : '';
			$commonPartTypeID = !empty($partTypeIDs) ? reset($partTypeIDs) : '';
			$commonDisposition = !empty($dispositions) ? reset($dispositions) : '';

			// Step 9: Find matching configuration detail assigned to current user
			$detailQuery = "SELECT pcd.*, pcm.CustomPalletID as ToCustomPalletID, pcm.BinName as ToBinName,
							cp.*, d.disposition as ToDisposition, pt.parttype,
							fd.disposition as FromDispositionName
							FROM pick_configuration_details pcd
							LEFT JOIN pick_configuration_details_custompallet_mapping pcm ON pcd.DetailID = pcm.DetailID
							LEFT JOIN custompallet cp ON pcm.CustomPalletID = cp.CustomPalletID
							LEFT JOIN disposition d ON cp.disposition_id = d.disposition_id
							LEFT JOIN parttype pt ON pcd.parttypeid = pt.parttypeid
							LEFT JOIN disposition fd ON pcd.FromDispositionID = fd.disposition_id
							WHERE pcd.ConfigurationID = '$configurationID'
							AND pcd.mpn = '$commonMPN'
							AND pcd.parttypeid = '$commonPartTypeID'
							AND pcd.FromDispositionID = '$commonDisposition'
							AND pcm.CustomPalletID IS NOT NULL
							AND pcm.UserId = '$currentUserID'";

			$detailResult = mysqli_query($this->connectionlink, $detailQuery);

			if (!$detailResult) {
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($detailResult) == 0) {
				// Get disposition name for error message
				$dispositionName = '';
				if (!empty($allSerials)) {
					$firstSerial = reset($allSerials);
					$dispositionName = $firstSerial['DispositionName'] ?: 'Unknown';
				}

				// Get part type name for error message
				$partTypeName = '';
				if (!empty($allSerials)) {
					$firstSerial = reset($allSerials);
					$partTypeName = $firstSerial['part_type'] ?: 'Unknown';
				}

				$json['Result'] = "No matching configuration detail found for MPN: $commonMPN, PartType: $partTypeName, Disposition: $dispositionName";
				return json_encode($json);
			}

			$configDetail = mysqli_fetch_assoc($detailResult);
			$toCustomPalletID = $configDetail['ToCustomPalletID'];
			$toBinName = $configDetail['ToBinName'];

			// Step 10: Validate destination bin
			if (!$toCustomPalletID) {
				$json['Result'] = 'No destination bin mapped for this configuration detail';
				return json_encode($json);
			}

			// Validate destination bin status
			if ($configDetail['StatusID'] == 7) {
				$json['Result'] = 'Destination bin is shipped and cannot receive serials';
				return json_encode($json);
			}

			if ($configDetail['StatusID'] == 5) {
				$json['Result'] = 'Destination bin is deleted and cannot receive serials';
				return json_encode($json);
			}

			// Check if destination bin is closed
			if ($configDetail['SealID'] != '' && $configDetail['SealID'] != NULL) {
				$json['Result'] = 'Destination bin is closed and cannot receive serials';
				return json_encode($json);
			}

			// Step 11: Validate facility match
			if ($configDetail['FacilityID'] != $currentFacilityID) {
				$json['Result'] = 'Destination bin facility does not match your facility';
				return json_encode($json);
			}

			// Step 12: Validate maximum limit (only if MaxLimitRequired = 1)
			$assetsToMove = count($allSerials);
			if ($configDetail['MaxLimitRequired'] == '1') {
				$currentAssetsInToBin = intval($configDetail['AssetsCount']);
				$maxAssetsAllowed = intval($configDetail['MaximumAssets']);

				if (($currentAssetsInToBin + $assetsToMove) > $maxAssetsAllowed) {
					$availableSpace = $maxAssetsAllowed - $currentAssetsInToBin;
					$json['Result'] = "Destination bin does not have enough space. Available space: $availableSpace, Assets to move: $assetsToMove. Maximum capacity: $maxAssetsAllowed";
					return json_encode($json);
				}
			}

			// Step 13: Validate customer lock
			if ($configDetail['CustomerLock'] == '1') {
				$destinationCustomerID = $configDetail['AWSCustomerID'];

				// Get unique customers from source serials
				$sourceCustomers = array_unique(array_column($allSerials, 'AWSCustomerID'));
				$sourceCustomers = array_filter($sourceCustomers); // Remove empty values

				if ($destinationCustomerID == NULL || $destinationCustomerID == '' || $destinationCustomerID == '0') {
					// Destination bin is empty, check if all source records belong to same customer
					if (count($sourceCustomers) > 1) {
						$json['Result'] = 'Source bin contains records from multiple customers. Cannot move to customer-locked bin.';
						return json_encode($json);
					}
				} else {
					// Destination bin has a customer, validate all source records match
					foreach ($sourceCustomers as $sourceCustomer) {
						if ($sourceCustomer != $destinationCustomerID) {
							$json['Result'] = 'Source bin contains records from different customer than destination bin. Cannot move to customer-locked bin.';
							return json_encode($json);
						}
					}
				}
			}

			// Step 14: Validate pick quantity limits
			$currentPickCompleted = intval($configDetail['PickCompleted']);
			$pickQuantity = intval($configDetail['PickQuantity']);

			if (($currentPickCompleted + $assetsToMove) > $pickQuantity) {
				$availablePicks = $pickQuantity - $currentPickCompleted;
				$json['Result'] = "Pick quantity exceeded. Available picks: $availablePicks, Assets to move: $assetsToMove. Total pick quantity: $pickQuantity";
				return json_encode($json);
			}

			// Step 15: All validations passed, perform bulk movement
			$fromCustomPalletID = $sourceBin['CustomPalletID'];
			$fromBinName = $sourceBin['BinName'];

			// Handle shipping status updates based on destination bin shipping status
			$fromShippingID = $sourceBin['ShippingID'];
			$toShippingID = $configDetail['ShippingID'];

			// Update status based on shipping context
			if (($fromShippingID == '' || $fromShippingID == NULL) && ($toShippingID != '' && $toShippingID != NULL)) {
				// Moving to shipment bin - update StatusID to shipped status
				$assetStatusID = '8';
				$serverStatusID = '8';
				$mediaStatusID = '5';
			} else if (($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID == '' || $toShippingID == NULL)) {
				// Moving from shipment bin - update StatusID to active
				$assetStatusID = '1';
				$serverStatusID = '1';
				$mediaStatusID = '1';
			} else {
				// Same shipping context - maintain current status but update to appropriate shipped status if in shipment
				if ($toShippingID != '' && $toShippingID != NULL) {
					$assetStatusID = '8';
					$serverStatusID = '8';
					$mediaStatusID = '5';
				} else {
					$assetStatusID = '1';
					$serverStatusID = '1';
					$mediaStatusID = '1';
				}
			}

			// Bulk update assets
			$updateAssetQuery = "UPDATE asset SET
								CustomPalletID = '$toCustomPalletID',
								StatusID = '$assetStatusID',
								ShippingID = " . ($toShippingID ? "'$toShippingID'" : "''") . ",
								DateUpdated = NOW(),
								UpdatedBy = '$currentUserID'
								WHERE CustomPalletID = '$fromCustomPalletID'";

			$assetUpdateResult = mysqli_query($this->connectionlink, $updateAssetQuery);
			if (!$assetUpdateResult) {
				$json['Result'] = 'Error updating assets: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$assetCount = mysqli_affected_rows($this->connectionlink);

			// Bulk update servers
			$updateServerQuery = "UPDATE speed_server_recovery SET
								 CustomPalletID = '$toCustomPalletID',
								 StatusID = '$serverStatusID',
								 ShippingID = " . ($toShippingID ? "'$toShippingID'" : "''") . ",
								 UpdatedDate = NOW(),
								 UpdatedBy = '$currentUserID'
								 WHERE CustomPalletID = '$fromCustomPalletID'";

			$serverUpdateResult = mysqli_query($this->connectionlink, $updateServerQuery);
			if (!$serverUpdateResult) {
				$json['Result'] = 'Error updating servers: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$serverCount = mysqli_affected_rows($this->connectionlink);

			// Bulk update media
			$updateMediaQuery = "UPDATE speed_media_recovery SET
								CustomPalletID = '$toCustomPalletID',
								StatusID = '$mediaStatusID',
								ShippingID = " . ($toShippingID ? "'$toShippingID'" : "''") . ",
								UpdatedDate = NOW(),
								UpdatedBy = '$currentUserID'
								WHERE CustomPalletID = '$fromCustomPalletID'";

			$mediaUpdateResult = mysqli_query($this->connectionlink, $updateMediaQuery);
			if (!$mediaUpdateResult) {
				$json['Result'] = 'Error updating media: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$mediaCount = mysqli_affected_rows($this->connectionlink);

			$totalMoved = $assetCount + $serverCount + $mediaCount;

			// Step 16: Update custompallet_items table
			$this->updateCustomPalletItems($fromCustomPalletID, $toCustomPalletID);

			// Step 17: Update custompallet asset counts
			$this->updateCustomPalletCounts($fromCustomPalletID, $toCustomPalletID, $totalMoved);

			// Step 18: Manage shipping_container_serials based on shipping context
			$this->manageShippingContainerSerials($allSerials, $fromShippingID, $toShippingID, $toCustomPalletID, $toBinName, $fromCustomPalletID, $fromBinName);

			// Step 19: Insert tracking records
			$this->insertPickToolMovementTracking($allSerials, $fromCustomPalletID, $toCustomPalletID, $fromBinName, $toBinName, $configurationID, $currentUserID);

			// Step 20: Update pick progress
			$updatePickProgressQuery = "UPDATE pick_configuration_details SET
									   PickCompleted = PickCompleted + $totalMoved,
									   UpdatedDate = NOW(),
									   UpdatedBy = '$currentUserID'
									   WHERE DetailID = '{$configDetail['DetailID']}'";

			mysqli_query($this->connectionlink, $updatePickProgressQuery);

			// Step 21: Return success
			$json['Success'] = true;
			$json['Result'] = "Successfully moved $totalMoved serials from $fromBinName to $toBinName (Assets: $assetCount, Servers: $serverCount, Media: $mediaCount)";

			return json_encode($json);			

		} catch (Exception $e) {
			$json['Result'] = 'Error: ' . $e->getMessage();
			return json_encode($json);
		}
	}

	private function insertPickToolMovementTracking($allSerials, $fromCustomPalletID, $toCustomPalletID, $fromBinName, $toBinName, $configurationID, $currentUserID) {
		// Generate event ID and batch flag for grouping related movements
		$event_id = rand(1000000000, 9999999999);
		$batch_event_flag = 'Y';
		$facilityID = $_SESSION['user']['FacilityID'];

		// Since assets are already moved, we need to use the $allSerials data to create tracking records
		foreach ($allSerials as $serial) {
			$assetType = $serial['AssetType'];
			$serialNumber = mysqli_real_escape_string($this->connectionlink, $serial['SerialNumber']);
			$universalModelNumber = mysqli_real_escape_string($this->connectionlink, $serial['UniversalModelNumber']);
			$partType = mysqli_real_escape_string($this->connectionlink, $serial['part_type']);
			$dispositionID = $serial['disposition_id'];
			$awsCustomerID = $serial['AWSCustomerID'];
			$cooID = $serial['COOID'];

			// Insert asset tracking records for each asset type
			if ($assetType == 'Asset') {
				$assetTrackingAction = "Asset Moved from BIN, (BIN ID : $fromBinName) to BIN, (BIN ID : $toBinName) in Pick Tool ProcessSourceBin";
				$assetTrackingQuery = "INSERT INTO asset_tracking (AssetScanID, Action, Description, UniqueID, CreatedDate, CreatedBy)
									   VALUES ('{$serial['ID']}', '$assetTrackingAction', '', '', NOW(), '$currentUserID')";
				mysqli_query($this->connectionlink, $assetTrackingQuery);

				// Insert picktool_asset_movement record for asset
				$assetMovementQuery = "INSERT INTO picktool_asset_movement (
										AssetScanID, SerialNumber, FromCustomPalletID, ToCustomPalletID, FacilityID,
										UniversalModelNumber, part_type, disposition_id, CreatedDate, CreatedBy,
										MovementType, FromBinName, ToBinName, ConfigurationID, event_id, batch_event_flag
									   ) VALUES (
										'{$serial['ID']}', '$serialNumber', '$fromCustomPalletID', '$toCustomPalletID', '$facilityID',
										'$universalModelNumber', '$partType', '$dispositionID', NOW(), '$currentUserID',
										'ProcessSourceBin', '$fromBinName', '$toBinName', '$configurationID', '$event_id', '$batch_event_flag'
									   )";
				mysqli_query($this->connectionlink, $assetMovementQuery);
			}
			elseif ($assetType == 'Server') {
				// Insert server tracking record
				$serverTrackingAction = "Server Moved from BIN, (BIN ID : $fromBinName) to BIN, (BIN ID : $toBinName) in Pick Tool ProcessSourceBin";
				$serverTrackingQuery = "INSERT INTO speed_server_recovery_tracking (ServerID, Action, Description, UniqueID, CreatedDate, CreatedBy)
									    VALUES ('{$serial['ID']}', '$serverTrackingAction', '', '', NOW(), '$currentUserID')";
				mysqli_query($this->connectionlink, $serverTrackingQuery);

				// Insert picktool_asset_movement record for server
				$serverMovementQuery = "INSERT INTO picktool_asset_movement (
										ServerID, SerialNumber, FromCustomPalletID, ToCustomPalletID, FacilityID,
										UniversalModelNumber, part_type, disposition_id, CreatedDate, CreatedBy,
										MovementType, FromBinName, ToBinName, ConfigurationID, event_id, batch_event_flag
									   ) VALUES (
										'{$serial['ID']}', '$serialNumber', '$fromCustomPalletID', '$toCustomPalletID', '$facilityID',
										'$universalModelNumber', '$partType', '$dispositionID', NOW(), '$currentUserID',
										'ProcessSourceBin', '$fromBinName', '$toBinName', '$configurationID', '$event_id', '$batch_event_flag'
									   )";
				mysqli_query($this->connectionlink, $serverMovementQuery);
			}
			elseif ($assetType == 'Media') {
				// Insert media tracking record
				$mediaTrackingAction = "Media Moved from BIN, (BIN ID : $fromBinName) to BIN, (BIN ID : $toBinName) in Pick Tool ProcessSourceBin";
				$mediaTrackingQuery = "INSERT INTO speed_media_recovery_tracking (MediaID, Action, Description, UniqueID, CreatedDate, CreatedBy)
									   VALUES ('{$serial['ID']}', '$mediaTrackingAction', '', '', NOW(), '$currentUserID')";
				mysqli_query($this->connectionlink, $mediaTrackingQuery);

				// Insert picktool_asset_movement record for media
				$mediaMovementQuery = "INSERT INTO picktool_asset_movement (
										MediaID, SerialNumber, FromCustomPalletID, ToCustomPalletID, FacilityID,
										UniversalModelNumber, part_type, disposition_id, CreatedDate, CreatedBy,
										MovementType, FromBinName, ToBinName, ConfigurationID, event_id, batch_event_flag
									   ) VALUES (
										'{$serial['ID']}', '$serialNumber', '$fromCustomPalletID', '$toCustomPalletID', '$facilityID',
										'$universalModelNumber', '$partType', '$dispositionID', NOW(), '$currentUserID',
										'ProcessSourceBin', '$fromBinName', '$toBinName', '$configurationID', '$event_id', '$batch_event_flag'
									   )";
				mysqli_query($this->connectionlink, $mediaMovementQuery);
			}
		}
	}

	private function manageShippingContainerSerials($allSerials, $fromShippingID, $toShippingID, $toCustomPalletID, $toBinName, $fromCustomPalletID, $fromBinName) {
		// Case 1: Moving from non-shipment bin to shipment bin - INSERT into shipping_container_serials
		if (($fromShippingID == '' || $fromShippingID == NULL) && ($toShippingID != '' && $toShippingID != NULL)) {
			foreach ($allSerials as $serial) {
				$this->insertShippingContainerSerial($serial, $toShippingID, $toCustomPalletID, $toBinName, $fromCustomPalletID, $fromBinName);
			}
		}
		// Case 2: Moving from shipment bin to non-shipment bin - DELETE from shipping_container_serials
		else if (($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID == '' || $toShippingID == NULL)) {
			// Use bulk delete by CustomPalletID instead of individual deletes
			$this->deleteShippingContainerSerialsByBin($fromCustomPalletID);
		}
		// Case 3: Moving between different shipment bins - UPDATE shipping_container_serials
		else if (($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID != '' && $toShippingID != NULL) && ($fromShippingID != $toShippingID)) {
			// Use bulk update by CustomPalletID instead of individual updates
			$this->updateShippingContainerSerialsByBin($fromCustomPalletID, $toCustomPalletID, $toBinName);
		}
		// Case 4: Moving within same shipment bin - UPDATE bin information only
		else if (($fromShippingID != '' && $fromShippingID != NULL) && ($toShippingID != '' && $toShippingID != NULL) && ($fromShippingID == $toShippingID)) {
			// Use bulk update by CustomPalletID for same shipment moves
			$this->updateShippingContainerSerialsByBin($fromCustomPalletID, $toCustomPalletID, $toBinName);
		}
	}

	private function insertShippingContainerSerial($serial, $toShippingID, $toCustomPalletID, $toBinName, $fromCustomPalletID, $fromBinName) {
		$assetType = $serial['AssetType'];
		$serialNumber = mysqli_real_escape_string($this->connectionlink, $serial['SerialNumber']);
		$universalModelNumber = mysqli_real_escape_string($this->connectionlink, $serial['UniversalModelNumber']);
		$partType = mysqli_real_escape_string($this->connectionlink, $serial['part_type']);
		$currentUserID = $_SESSION['user']['UserId'];

		if ($assetType == 'Asset') {
			$insertQuery = "INSERT INTO shipping_container_serials (
								AssetScanID, SerialNumber, Notes, StatusID, CreatedDate, CreatedBy,
								UniversalModelNumber, part_type, CustomPalletID, BinName,
								FromCustomPalletID, FromBinName, COO, Quantity
							) SELECT
								a.AssetScanID, a.SerialNumber, 'Added via ProcessSourceBin', a.StatusID, NOW(), '$currentUserID',
								a.UniversalModelNumber, a.part_type, '$toCustomPalletID', '$toBinName',
								'$fromCustomPalletID', '$fromBinName', c.COO, '1'
							FROM asset a
							LEFT JOIN COO c ON a.COOID = c.COOID
							WHERE a.AssetScanID = '{$serial['ID']}'";
		} else if ($assetType == 'Server') {
			$insertQuery = "INSERT INTO shipping_container_serials (
								ServerID, SerialNumber, Notes, StatusID, CreatedDate, CreatedBy,
								UniversalModelNumber, part_type, CustomPalletID, BinName,
								FromCustomPalletID, FromBinName, COO, Quantity
							) SELECT
								s.ServerID, s.ServerSerialNumber, 'Added via ProcessSourceBin', s.StatusID, NOW(), '$currentUserID',
								s.MPN, s.Type, '$toCustomPalletID', '$toBinName',
								'$fromCustomPalletID', '$fromBinName', c.COO, '1'
							FROM speed_server_recovery s
							LEFT JOIN COO c ON s.COOID = c.COOID
							WHERE s.ServerID = '{$serial['ID']}'";
		} else if ($assetType == 'Media') {
			$insertQuery = "INSERT INTO shipping_container_serials (
								MediaID, SerialNumber, Notes, StatusID, CreatedDate, CreatedBy,
								UniversalModelNumber, part_type, CustomPalletID, BinName,
								FromCustomPalletID, FromBinName, COO, Quantity
							) SELECT
								m.MediaID, m.MediaSerialNumber, 'Added via ProcessSourceBin', m.StatusID, NOW(), '$currentUserID',
								m.MediaMPN, m.MediaType, '$toCustomPalletID', '$toBinName',
								'$fromCustomPalletID', '$fromBinName', c.COO, '1'
							FROM speed_media_recovery m
							LEFT JOIN COO c ON m.COOID = c.COOID
							WHERE m.MediaID = '{$serial['ID']}'";
		}

		if (isset($insertQuery)) {
			mysqli_query($this->connectionlink, $insertQuery);
		}
	}

	private function deleteShippingContainerSerialsByBin($fromCustomPalletID) {
		// Delete all shipping container serials for the entire bin at once
		$deleteQuery = "DELETE FROM shipping_container_serials WHERE CustomPalletID = '$fromCustomPalletID'";
		mysqli_query($this->connectionlink, $deleteQuery);
	}

	private function updateShippingContainerSerialsByBin($fromCustomPalletID, $toCustomPalletID, $toBinName) {
		// Update all shipping container serials for the entire bin at once
		$currentUserID = $_SESSION['user']['UserId'];
		$updateQuery = "UPDATE shipping_container_serials SET
						CustomPalletID = '$toCustomPalletID',
						BinName = '$toBinName',
						UpdatedDate = NOW(),
						UpdatedBy = '$currentUserID'
						WHERE CustomPalletID = '$fromCustomPalletID'";
		mysqli_query($this->connectionlink, $updateQuery);
	}

	private function updateCustomPalletItems($fromCustomPalletID, $toCustomPalletID) {
		// Update custompallet_items table to reflect new bin assignment
		$updateItemsQuery = "UPDATE custompallet_items SET
							CustomPalletID = '$toCustomPalletID'
							WHERE CustomPalletID = '$fromCustomPalletID'";
		mysqli_query($this->connectionlink, $updateItemsQuery);
	}

	private function updateCustomPalletCounts($fromCustomPalletID, $toCustomPalletID, $totalMoved) {
		// Decrease count in source bin
		$decreaseSourceQuery = "UPDATE custompallet SET
								AssetsCount = AssetsCount - $totalMoved
								WHERE CustomPalletID = '$fromCustomPalletID'";
		mysqli_query($this->connectionlink, $decreaseSourceQuery);

		// Increase count in destination bin
		$increaseDestQuery = "UPDATE custompallet SET
							  AssetsCount = AssetsCount + $totalMoved
							  WHERE CustomPalletID = '$toCustomPalletID'";
		mysqli_query($this->connectionlink, $increaseDestQuery);
	}

	// Bin Management Methods for ProcessPickPath

	public function CreateBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			// Validate required fields
			if (empty($data['BinName']) || empty($data['idPackage']) || empty($data['FacilityID'])) {
				$json['Success'] = false;
				$json['Result'] = 'Bin Name, Bin Type, and Facility are required';
				return json_encode($json);
			}

			// Validate bin name prefix based on container type requirements
			$binName = mysqli_real_escape_string($this->connectionlink, $data['BinName']);
			$packageId = mysqli_real_escape_string($this->connectionlink, $data['idPackage']);

			// Get container type details including PrefixRequiredForBinName and PrefixValue
			$packageQuery = "SELECT packageName, PrefixRequiredForBinName, PrefixValue FROM package WHERE idPackage = '$packageId'";
			$packageResult = mysqli_query($this->connectionlink, $packageQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Error validating container type: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($packageResult) > 0) {
				$packageData = mysqli_fetch_assoc($packageResult);

				// Check if prefix is required for this container type
				if ($packageData['PrefixRequiredForBinName'] == '1') {
					// Use PrefixValue if available, otherwise fall back to first 3 characters of packageName
					$requiredPrefix = !empty($packageData['PrefixValue'])
						? strtoupper(trim($packageData['PrefixValue']))
						: strtoupper(substr($packageData['packageName'], 0, 3));

					$binNamePrefix = strtoupper(substr($binName, 0, strlen($requiredPrefix)));

					if ($binNamePrefix !== $requiredPrefix) {
						$json['Success'] = false;
						$json['Result'] = 'Bin name must start with "' . $requiredPrefix . '" (prefix for container type "' . $packageData['packageName'] . '")';
						return json_encode($json);
					}
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid container type selected';
				return json_encode($json);
			}

			// Check if bin name already exists
			$checkQuery = "SELECT COUNT(*) as count FROM custompallet WHERE BinName = '$binName'";
			$checkResult = mysqli_query($this->connectionlink, $checkQuery);
			$checkRow = mysqli_fetch_assoc($checkResult);

			if ($checkRow['count'] > 0) {
				$json['Success'] = false;
				$json['Result'] = 'Bin name already exists. Please choose a different name.';
				return json_encode($json);
			}

			$userId = $_SESSION['user']['UserId'];
			$facilityID = mysqli_real_escape_string($this->connectionlink, $data['FacilityID']);
			$locationType = isset($data['LocationType']) ? mysqli_real_escape_string($this->connectionlink, $data['LocationType']) : 'WIP';
			$description = isset($data['Notes']) ? mysqli_real_escape_string($this->connectionlink, $data['Notes']) : '';
			$statusID = isset($data['StatusID']) ? mysqli_real_escape_string($this->connectionlink, $data['StatusID']) : '1';
			$acceptAllDisposition = isset($data['AcceptAllDisposition']) ? mysqli_real_escape_string($this->connectionlink, $data['AcceptAllDisposition']) : '0';
			$maxLimitRequired = isset($data['MaxLimitRequired']) ? mysqli_real_escape_string($this->connectionlink, $data['MaxLimitRequired']) : '0';
			$customerLock = isset($data['CustomerLock']) ? mysqli_real_escape_string($this->connectionlink, $data['CustomerLock']) : '0';
			$referenceIDRequired = isset($data['ReferenceIDRequired']) ? mysqli_real_escape_string($this->connectionlink, $data['ReferenceIDRequired']) : '0';
			$dispositionID = isset($data['disposition_id']) ? mysqli_real_escape_string($this->connectionlink, $data['disposition_id']) : 'NULL';
			$locationID = isset($data['LocationID']) && !empty($data['LocationID']) ? mysqli_real_escape_string($this->connectionlink, $data['LocationID']) : 'NULL';
			$groupID = isset($data['LocationGroupID']) && !empty($data['LocationGroupID']) ? mysqli_real_escape_string($this->connectionlink, $data['LocationGroupID']) : 'NULL';

			// If GroupID is not provided but LocationGroupName is provided (scanned), try to find the GroupID
			if (($groupID == 'NULL' || empty($groupID)) && isset($data['LocationGroupName']) && !empty($data['LocationGroupName'])) {
				$locationGroupName = mysqli_real_escape_string($this->connectionlink, $data['LocationGroupName']);
				$groupLookupQuery = "SELECT lg.GroupID, lg.LocationID FROM location_group lg
									 INNER JOIN location l ON lg.GroupID = l.GroupID
									 WHERE lg.GroupName = '$locationGroupName'
									 AND l.FacilityID = '$facilityID'
									 AND l.LocationType = 'WIP'
									 AND l.LocationStatus = '1'
									 LIMIT 1";
				$groupLookupResult = mysqli_query($this->connectionlink, $groupLookupQuery);

				if ($groupLookupResult && mysqli_num_rows($groupLookupResult) > 0) {
					$groupLookupRow = mysqli_fetch_assoc($groupLookupResult);
					$groupID = $groupLookupRow['GroupID'];
					$locationID = $groupLookupRow['LocationID'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Location Group "' . $data['LocationGroupName'] . '" not found or not available for this facility';
					return json_encode($json);
				}
			}

			// If LocationGroup field is provided (from autocomplete text), try to find the GroupID
			if (($groupID == 'NULL' || empty($groupID)) && isset($data['LocationGroup']) && !empty($data['LocationGroup'])) {
				$locationGroupName = mysqli_real_escape_string($this->connectionlink, $data['LocationGroup']);
				$groupLookupQuery = "SELECT lg.GroupID, lg.LocationID FROM location_group lg
									 INNER JOIN location l ON lg.GroupID = l.GroupID
									 WHERE lg.GroupName = '$locationGroupName'
									 AND l.FacilityID = '$facilityID'
									 AND l.LocationType = 'WIP'
									 AND l.LocationStatus = '1'
									 LIMIT 1";
				$groupLookupResult = mysqli_query($this->connectionlink, $groupLookupQuery);

				if ($groupLookupResult && mysqli_num_rows($groupLookupResult) > 0) {
					$groupLookupRow = mysqli_fetch_assoc($groupLookupResult);
					$groupID = $groupLookupRow['GroupID'];
					$locationID = $groupLookupRow['LocationID'];
				}
			}

			// If GroupID is provided, find and assign an available location (same as failure analysis)
			if (!empty($groupID) && $groupID != 'NULL') {
				// Check if there are empty locations in the group
				$checkQuery = "SELECT count(*) FROM location WHERE GroupID = '$groupID' AND Locked = '2' AND LocationStatus = '1'";
				$checkResult = mysqli_query($this->connectionlink, $checkQuery);

				if (!$checkResult) {
					$json['Success'] = false;
					$json['Result'] = 'Database error while checking locations: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($checkResult) > 0) {
					$checkRow = mysqli_fetch_assoc($checkResult);
					if ($checkRow['count(*)'] < 1) {
						$json['Success'] = false;
						$json['Result'] = 'No empty locations available in selected Location Group';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}

				// Get the first available location
				$locationQuery = "SELECT LocationID, LocationName
								  FROM location
								  WHERE GroupID = '$groupID'
								  AND Locked = '2'
								  AND LocationStatus = '1'
								  ORDER BY LocationName
								  LIMIT 1";
				$locationResult = mysqli_query($this->connectionlink, $locationQuery);

				if (!$locationResult) {
					$json['Success'] = false;
					$json['Result'] = 'Database error while searching for locations: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($locationResult) > 0) {
					$locationRow = mysqli_fetch_assoc($locationResult);
					$locationID = $locationRow['LocationID'];

					// Lock the location
					$lockQuery = "UPDATE location
								  SET Locked = '1',
									  currentItemType = 'Bin',
									  currentItemID = '$binName'
								  WHERE LocationID = '$locationID'";
					$lockResult = mysqli_query($this->connectionlink, $lockQuery);

					if (!$lockResult) {
						$json['Success'] = false;
						$json['Result'] = 'Failed to lock location: ' . mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				} else {
					// No available location found - return error
					$json['Success'] = false;
					$json['Result'] = 'No empty locations available in selected Location Group';
					return json_encode($json);
				}
			} else {
				// No GroupID provided, set LocationID to NULL
				$locationID = 'NULL';
			}

			// Insert new bin (using failure analysis structure)

			$insertQuery = "INSERT INTO custompallet (
				BinName,
				idPackage,
				BinType,
				FacilityID,
				LocationType,
				LocationID,
				GroupID,
				Description,
				StatusID,
				AcceptAllDisposition,
				MaxLimitRequired,
				CustomerLock,
				ReferenceIDRequired,
				disposition_id,
				AssetsCount,
				CreatedDate,
				CreatedBy
			) VALUES (
				'$binName',
				'$packageId',
				'Physical',
				'$facilityID',
				'$locationType',
				$locationID,
				$groupID,
				'$description',
				'$statusID',
				'$acceptAllDisposition',
				'$maxLimitRequired',
				'$customerLock',
				'$referenceIDRequired',
				$dispositionID,
				0,
				NOW(),
				'$userId'
			)";

			mysqli_query($this->connectionlink, $insertQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$customPalletID = mysqli_insert_id($this->connectionlink);

			// Add tracking record with disposition and location group information (same as failure analysis)
			$trackingAction = "Bin was Created from ProcessPickPath module";

			// Get disposition name if disposition_id is provided
			$dispositionName = '';
			if ($dispositionID != 'NULL') {
				$dispQuery = "SELECT disposition FROM disposition WHERE disposition_id = $dispositionID";
				$dispResult = mysqli_query($this->connectionlink, $dispQuery);
				if ($dispResult && mysqli_num_rows($dispResult) > 0) {
					$dispRow = mysqli_fetch_assoc($dispResult);
					$dispositionName = $dispRow['disposition'];
				}
			}

			// Get location group name if groupID is provided
			$locationGroupName = '';
			if ($groupID != 'NULL') {
				$groupQuery = "SELECT GroupName FROM location_group WHERE GroupID = $groupID";
				$groupResult = mysqli_query($this->connectionlink, $groupQuery);
				if ($groupResult && mysqli_num_rows($groupResult) > 0) {
					$groupRow = mysqli_fetch_assoc($groupResult);
					$locationGroupName = $groupRow['GroupName'];
				}
			}

			// Build detailed tracking action
			$trackingDetails = array();
			if (!empty($dispositionName)) {
				$trackingDetails[] = "Disposition: $dispositionName";
			}
			if (!empty($locationGroupName)) {
				$trackingDetails[] = "Location Group: $locationGroupName";
			}

			if (!empty($trackingDetails)) {
				$trackingAction .= " (" . implode(", ", $trackingDetails) . ")";
			}

			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
							  VALUES ('$customPalletID', '$binName', '$trackingAction', NOW(), '$userId','ProcessPickPath')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = 'Bin created successfully with ID: ' . $customPalletID;
			$json['CustomPalletID'] = $customPalletID;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetBinPackageTypes($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			// Get facility ID from session or data
			$facilityID = isset($data['FacilityID']) ? $data['FacilityID'] : $_SESSION['user']['FacilityID'];

			// Get package types for bin creation (matching failure analysis implementation)
			$query = "SELECT idPackage, packageName FROM package WHERE Active = '1' AND FacilityID = '" . mysqli_real_escape_string($this->connectionlink, $facilityID) . "' ORDER BY packageName";
			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$packages = array();
			$i = 0;
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				while ($row = mysqli_fetch_assoc($result)) {
					$packages[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $packages;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No package types found";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetSessionFacility($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => array()
		);

		try {
			$facilityID = $_SESSION['user']['FacilityID'];
			$query = "SELECT FacilityID, FacilityName FROM facility WHERE FacilityID = '$facilityID'";
			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$facility = mysqli_fetch_assoc($result);
			if ($facility) {
				$json['Success'] = true;
				$json['Result'] = $facility;
			} else {
				$json['Result'] = 'Facility not found';
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateBinName($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => ''
		);

		try {
			// Get package prefix
			$packageQuery = "SELECT PrefixValue FROM package WHERE idPackage = '" . mysqli_real_escape_string($this->connectionlink, $data['PackageID']) . "'";
			$packageResult = mysqli_query($this->connectionlink, $packageQuery);
			$packageData = mysqli_fetch_assoc($packageResult);

			if (!$packageData) {
				$json['Result'] = 'Invalid package type';
				return json_encode($json);
			}

			$prefix = $packageData['PrefixValue'];
			$attempts = 0;
			$maxAttempts = 10;

			do {
				// Generate 10-digit random number
				$randomNumber = str_pad(mt_rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
				$binName = $prefix . $randomNumber;

				// Check if this bin name already exists
				$checkQuery = "SELECT COUNT(*) as count FROM custompallet WHERE BinName = '$binName'";
				$checkResult = mysqli_query($this->connectionlink, $checkQuery);
				$checkRow = mysqli_fetch_assoc($checkResult);

				$attempts++;
			} while ($checkRow['count'] > 0 && $attempts < $maxAttempts);

			if ($attempts >= $maxAttempts) {
				$json['Result'] = 'Unable to generate unique bin name after multiple attempts';
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = $binName;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetBinDetails($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			// Validate required fields
			if (empty($data['CustomPalletID'])) {
				$json['Success'] = false;
				$json['Result'] = 'CustomPalletID is required';
				return json_encode($json);
			}

			$customPalletID = mysqli_real_escape_string($this->connectionlink, $data['CustomPalletID']);

			// Get bin details with current location information
			$query = "SELECT
						cp.CustomPalletID,
						cp.BinName,
						cp.FacilityID,
						cp.LocationType,
						cp.LocationID,
						cp.GroupID,
						lg.GroupName as CurrentLocationGroup,
						l.LocationName as CurrentLocationName,
						f.FacilityName
					  FROM custompallet cp
					  LEFT JOIN location_group lg ON cp.GroupID = lg.GroupID
					  LEFT JOIN location l ON cp.LocationID = l.LocationID
					  LEFT JOIN facility f ON cp.FacilityID = f.FacilityID
					  WHERE cp.CustomPalletID = '$customPalletID'";

			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($result) > 0) {
				$binDetails = mysqli_fetch_assoc($result);
				$json['Success'] = true;
				$json['Result'] = $binDetails;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Bin not found';
			}

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function MoveBinToNewLocationGroup($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {

			//Start get Bin Details
			$query = "select * from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "Bin Status is not active";
					return json_encode($json);
				}
				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = "Bin Facility is different from User Facility";
					return json_encode($json);
				}

				//Start check if valid Group
				$groupName = isset($data['GroupName']) ? $data['GroupName'] : $data['NewLocationGroup'];
				$query10 = "select GroupID,LocationType,BinTypeID,GroupName from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$groupName)."'";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row10 = mysqli_fetch_assoc($q10);
					if($row10['LocationType'] != 'WIP' && $row10['LocationType'] != 'Outbound Storage') {
						$json['Success'] = false;
						$json['Result'] = 'Only WIP and Outbound Storage Location groups are allowed';
						return json_encode($json);
					}
					$data['GroupID'] = $row10['GroupID'];
					$GroupName = $row10['GroupName'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}
				//End check if valid Group

				//Start get free location from group selected
				$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
				$q112 = mysqli_query($this->connectionlink,$query112);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row112 = mysqli_fetch_assoc($q112);
					$data['NewLocationID'] = $row112['LocationID'];
					$newLocationName = $row112['LocationName'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'No locations available, in selected group';
					return json_encode($json);
				}
				//End get free location from group selected


				$query2 = "update custompallet set LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$row['LocationID']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);
				}

				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Bin',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);
				}

				//Start Delete Bin from Pick Configuration mapping
				if (isset($data['DetailID']) && !empty($data['DetailID'])) {
					$detailID = mysqli_real_escape_string($this->connectionlink, $data['DetailID']);
					$userId = $_SESSION['user']['UserId'];
					$query2 = "DELETE from pick_configuration_details_custompallet_mapping where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' and DetailID = '".mysqli_real_escape_string($this->connectionlink,$data['DetailID'])."' and UserID = '".$userId."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}
				//End Delete Bin from Pick Configuration mapping

				//Start ProcessPickPath Tracking
				$currentLocationGroup = isset($data['CurrentLocationGroup']) ? $data['CurrentLocationGroup'] : '';
				$trackingAction = "BIN moved from ProcessPickPath module";
				if (!empty($currentLocationGroup)) {
					$trackingAction .= " (From: ".$currentLocationGroup." To: ".$GroupName.")";
				} else {
					$trackingAction .= " (To: ".$GroupName.")";
				}

				$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
								  VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."','ProcessPickPath')";
				$trackingResult = mysqli_query($this->connectionlink,$trackingQuery);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End ProcessPickPath Tracking

				$json['newLocationName'] = $newLocationName;
				$json['GroupName'] = $GroupName;
				$json['Success'] = true;
				$json['Result'] = 'Location Group Updated';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Bin";
				return json_encode($json);
			}
			//End get Bin Details

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function CloseBin($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No data'
		);

		try {
			// Basic validation
			if (!isset($data['CustomPalletID'])) {
				$json['Success'] = false;
				$json['Result'] = 'CustomPalletID is required';
				return json_encode($json);
			}

			$customPalletID = mysqli_real_escape_string($this->connectionlink, $data['CustomPalletID']);
			$sealID = isset($data['NewSealID']) ? mysqli_real_escape_string($this->connectionlink, $data['NewSealID']) : '';
			$weight = isset($data['BinWeight']) ? mysqli_real_escape_string($this->connectionlink, $data['BinWeight']) : '0';
			$controller = isset($data['AuditController']) ? mysqli_real_escape_string($this->connectionlink, $data['AuditController']) : '';
			$password = isset($data['Password']) ? mysqli_real_escape_string($this->connectionlink, $data['Password']) : '';
			$shippingID = isset($data['ShippingID']) ? mysqli_real_escape_string($this->connectionlink, trim($data['ShippingID'])) : '';
			$outboundLocationGroup = isset($data['OutboundLocationGroup']) ? mysqli_real_escape_string($this->connectionlink, trim($data['OutboundLocationGroup'])) : '';
			$userId = $_SESSION['user']['UserId'];

			// Validate shipping and location group requirements
			if (!empty($shippingID) && empty($outboundLocationGroup)) {
				$json['Success'] = false;
				$json['Result'] = 'Outbound Location Group is required when Shipping ID is provided';
				return json_encode($json);
			}

			// Validate required fields
			if (empty($sealID)) {
				$json['Success'] = false;
				$json['Result'] = 'Seal ID is required';
				return json_encode($json);
			}

			if (empty($controller)) {
				$json['Success'] = false;
				$json['Result'] = 'Controller is required';
				return json_encode($json);
			}

			if (empty($password)) {
				$json['Success'] = false;
				$json['Result'] = 'Password is required';
				return json_encode($json);
			}

			// Get bin details
			$binQuery = "SELECT * FROM custompallet WHERE CustomPalletID = '$customPalletID'";
			$binResult = mysqli_query($this->connectionlink, $binQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($binResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Bin not found';
				return json_encode($json);
			}

			$binData = mysqli_fetch_assoc($binResult);

			// Validate bin status
			if ($binData['StatusID'] != '1') {
				$json['Success'] = false;
				$json['Result'] = 'Bin is not active and cannot be closed';
				return json_encode($json);
			}

			// Validate that bin has assets before closing
			if (intval($binData['AssetsCount']) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Cannot close bin - bin contains no assets. Only bins with assets can be closed.';
				return json_encode($json);
			}

			// Validate that bin is not inside a parent
			if (!empty($binData['ParentCustomPalletID']) && $binData['ParentCustomPalletID'] > 0) {
				$json['Success'] = false;
				$json['Result'] = 'Bin cannot be closed while it is inside a parent bin. Please remove from parent first.';
				return json_encode($json);
			}

			//Start validate Controller
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['RemovalController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Removal Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			//End validate Controller

			// Start validate shipping and location if provided
			$shippingData = null;
			$availableLocationID = null;
			if (!empty($shippingID)) {
				// Validate shipping exists and is active
				$shippingQuery = "SELECT * FROM shipping WHERE ShippingID = '$shippingID' AND FacilityID = '".$_SESSION['user']['FacilityID']."'";
				$shippingResult = mysqli_query($this->connectionlink, $shippingQuery);

				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Database error validating shipping: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($shippingResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipping ID or Shipping ID does not belong to your facility';
					return json_encode($json);
				}

				$shippingData = mysqli_fetch_assoc($shippingResult);
				if ($shippingData['ShipmentStatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'Shipment is not active';
					return json_encode($json);
				}

				// Validate outbound location group and find available location
				$locationGroupQuery = "SELECT lg.GroupID FROM location_group lg WHERE lg.GroupName = '$outboundLocationGroup' AND lg.FacilityID = '".$_SESSION['user']['FacilityID']."' AND lg.LocationType = 'Outbound Storage'";
				$locationGroupResult = mysqli_query($this->connectionlink, $locationGroupQuery);

				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Database error validating location group: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($locationGroupResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Outbound Location Group or Location Group does not belong to your facility';
					return json_encode($json);
				}

				$locationGroupData = mysqli_fetch_assoc($locationGroupResult);

				// Find an available location in the specified group
				$availableLocationQuery = "SELECT LocationID, LocationName FROM location WHERE GroupID = '".$locationGroupData['GroupID']."' AND LocationStatus = '1' AND Locked = '2' ORDER BY LocationName LIMIT 1";
				$availableLocationResult = mysqli_query($this->connectionlink, $availableLocationQuery);

				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Database error finding available location: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($availableLocationResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = 'No available locations found in the specified Outbound Location Group';
					return json_encode($json);
				}

				$availableLocationData = mysqli_fetch_assoc($availableLocationResult);
				$availableLocationID = $availableLocationData['LocationID'];
			}
			//End validate shipping and location

			// Check if seal ID already exists
			$sealCheckQuery = "SELECT CustomPalletID FROM custompallet WHERE SealID = '$sealID' AND CustomPalletID != '$customPalletID'";
			$sealCheckResult = mysqli_query($this->connectionlink, $sealCheckQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Database error checking seal ID: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($sealCheckResult) > 0) {
				$json['Success'] = false;
				$json['Result'] = 'Seal ID already exists. Please use a different seal ID.';
				return json_encode($json);
			}

			// Remove bin from pick configuration mapping since it's being closed
			$removedFromMapping = false;
			if (isset($data['DetailID']) && !empty($data['DetailID'])) {
				$detailID = mysqli_real_escape_string($this->connectionlink, $data['DetailID']);
				$removeFromMappingQuery = "DELETE FROM pick_configuration_details_custompallet_mapping WHERE CustomPalletID = '$customPalletID' AND DetailID = '$detailID' AND UserID = '$userId'";
				$removeFromMappingResult = mysqli_query($this->connectionlink, $removeFromMappingQuery);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Failed to remove bin from pick configuration mapping: ' . mysqli_error($this->connectionlink) . ' Query: ' . $removeFromMappingQuery;
					return json_encode($json);
				}

				// Check if bin was removed from pick configuration mapping
				$affectedRows = mysqli_affected_rows($this->connectionlink);
				if ($affectedRows > 0) {
					$removedFromMapping = true;
				}


			}

			// Update bin to closed status and optionally add to shipment
			$statusID = !empty($shippingID) ? '6' : '3'; // StatusID 6 if shipped, 3 if just closed



			$updateQuery = "UPDATE custompallet SET
				StatusID = '$statusID',
				LastModifiedDate = NOW(),
				LastModifiedBy = '$userId',
				SealID = '$sealID',
				ContainerWeight = '$weight',
				RecentSealDate = NOW(),
				RecentSealBy = '$userId',
				ShippingControllerLoginID = '$controller'";

			// Add shipping and location fields if provided
			if (!empty($shippingID)) {
				$updateQuery .= ",
				ShippingID = '$shippingID',
				StatusModifiedDate = NOW(),
				StatusModifiedBy = '$userId',
				bin_added_to_shipment_time = NOW(),
				bin_added_to_shipment_by = '$userId'";

				if ($availableLocationID) {
					$updateQuery .= ",
					LocationID = '$availableLocationID'";
				}
			}

			$updateQuery .= " WHERE CustomPalletID = '$customPalletID'";

			$updateResult = mysqli_query($this->connectionlink, $updateQuery);

			if (!$updateResult) {
				$json['Success'] = false;
				$json['Result'] = 'Failed to close bin: ' . mysqli_error($this->connectionlink) . ' Query: ' . $updateQuery;
				return json_encode($json);
			}

			// Unlock the old location if bin was previously assigned to one
			if (!empty($binData['LocationID']) && $binData['LocationID'] > 0) {
				$unlockOldLocationQuery = "UPDATE location SET
					Locked = '2',
					currentItemType = NULL,
					currentItemID = NULL
					WHERE LocationID = '".$binData['LocationID']."'";

				$unlockOldLocationResult = mysqli_query($this->connectionlink, $unlockOldLocationQuery);
				if (!$unlockOldLocationResult) {
					error_log("Failed to unlock old location: " . mysqli_error($this->connectionlink));
				}
			}

			// Lock the new location if bin was assigned to one
			if (!empty($shippingID) && $availableLocationID) {

				$lockLocationQuery = "UPDATE location SET
					Locked = '1',
					currentItemType = 'Bin',
					currentItemID = '".$binData['BinName']."'
					WHERE LocationID = '$availableLocationID'";

				$lockLocationResult = mysqli_query($this->connectionlink, $lockLocationQuery);
				if (!$lockLocationResult) {
					// Log error but don't fail the operation
					error_log("Failed to lock new location: " . mysqli_error($this->connectionlink));
				}
			}

			// Add tracking record with detailed information
			$trackingAction = "Bin was Closed from ProcessPickPath module - Seal: $sealID, Weight: $weight, Controller: $controller";
			if ($removedFromMapping) {
				$trackingAction .= ", Removed from pick configuration mapping";
			}
			if (!empty($shippingID)) {
				$trackingAction .= ", Added to Shipment: $shippingID";
				if (!empty($outboundLocationGroup)) {
					$trackingAction .= ", Assigned to Location Group: $outboundLocationGroup";
				}
			}

			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
							  VALUES ('$customPalletID', '{$binData['BinName']}', '".mysqli_real_escape_string($this->connectionlink, $trackingAction)."', NOW(), '$userId', 'ProcessPickPath')";
			mysqli_query($this->connectionlink, $trackingQuery);

			// Close all child bins when parent is closed
			$childBinsQuery = "SELECT CustomPalletID, BinName FROM custompallet WHERE ParentCustomPalletID = '$customPalletID' AND StatusID = '1'";
			$childBinsResult = mysqli_query($this->connectionlink, $childBinsQuery);

			if ($childBinsResult && mysqli_num_rows($childBinsResult) > 0) {
				$childBinsCount = 0;
				while ($childBin = mysqli_fetch_assoc($childBinsResult)) {
					// Remove child bin from pick configuration mapping
					$removeChildFromMappingQuery = "DELETE FROM pick_configuration_details_custompallet_mapping WHERE CustomPalletID = '{$childBin['CustomPalletID']}' AND UserID = '$userId'";
					mysqli_query($this->connectionlink, $removeChildFromMappingQuery);
					$childRemovedFromMapping = (mysqli_affected_rows($this->connectionlink) > 0);

					// Close each child bin
					$closeChildQuery = "UPDATE custompallet SET
						StatusID = '3',
						LastModifiedDate = NOW(),
						LastModifiedBy = '$userId',
						SealID = '$sealID',
						ContainerWeight = '$weight',
						RecentSealDate = NOW(),
						RecentSealBy = '$userId',
						ShippingControllerLoginID = '$controller'
						WHERE CustomPalletID = '{$childBin['CustomPalletID']}'";

					$closeChildResult = mysqli_query($this->connectionlink, $closeChildQuery);

					if ($closeChildResult) {
						// Add tracking record for child bin
						$childTrackingAction = "Bin was Closed from ProcessPickPath module - Parent bin {$binData['BinName']} was closed, so child bin is also closed - Seal: $sealID, Controller: $controller";
						if ($childRemovedFromMapping) {
							$childTrackingAction .= ", Removed from pick configuration mapping";
						}
						$childTrackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
											   VALUES ('{$childBin['CustomPalletID']}', '{$childBin['BinName']}', '".mysqli_real_escape_string($this->connectionlink, $childTrackingAction)."', NOW(), '$userId', 'ProcessPickPath')";
						mysqli_query($this->connectionlink, $childTrackingQuery);
						$childBinsCount++;
					}
				}

				if ($childBinsCount > 0) {
					$json['Success'] = true;
					$resultMessage = "Bin closed successfully. Also closed $childBinsCount child bin(s).";
					if (!empty($shippingID)) {
						$resultMessage .= " Bin added to Shipment $shippingID";
					}
					$json['Result'] = $resultMessage;
				} else {
					$json['Success'] = true;
					$resultMessage = 'Bin closed successfully';
					if (!empty($shippingID)) {
						$resultMessage .= ". Bin added to Shipment $shippingID";
					}
					$json['Result'] = $resultMessage;
				}
			} else {
				$json['Success'] = true;
				$resultMessage = 'Bin closed successfully';
				if (!empty($shippingID)) {
					$resultMessage .= ". Bin added to Shipment $shippingID";
				}
				$json['Result'] = $resultMessage;
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	public function ConsolidateBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			$moduleName = 'ProcessPickPath';



			// Validate required fields
			if (empty($data['FromCustomPalletID'])) {
				$json['Success'] = false;
				$json['Result'] = 'FromCustomPalletID is required';
				return json_encode($json);
			}

			// Check if this is a bin-to-bin consolidation (ToBinName provided) or bin-to-shipment (ToShipmentContainer provided)
			$isBinToBin = !empty($data['ToBinName']) && $data['ToBinName'] != 'n/a' && trim($data['ToBinName']) != '';
			$isBinToShipment = !empty($data['ToShipmentContainer']) && $data['ToShipmentContainer'] != 'n/a' && trim($data['ToShipmentContainer']) != '';

			if (!$isBinToBin && !$isBinToShipment) {
				$json['Success'] = false;
				$json['Result'] = 'Either Destination Bin Name or Shipment Container is required for consolidation';
				return json_encode($json);
			}

			// Validate that From and To bins are different
			if(isset($data['FromBinName']) && $data['FromBinName'] == $data['ToBinName']) {
				$json['Success'] = false;
				$json['Result'] = 'From and To Bins should be different';
				return json_encode($json);
			}

			//Start check From CustomPallet Details
			$query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$from_cp = mysqli_fetch_assoc($q);
				$from_CustomPalletID = $from_cp['CustomPalletID'];
				if($from_cp['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'From BIN Status is not Active';
					return json_encode($json);
				}
				if($from_cp['AssetsCount'] == 0) {
					$json['Success'] = false;
					$json['Result'] = 'No Serials available in From BIN';
					return json_encode($json);
				}

				if($from_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'From BIN Facility is different from Users Facility';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid From BIN";
				return json_encode($json);
			}

			if($isBinToBin) {//Bin to Bin Consolidation

				//Start get to custompallet details
				$query6 = "select CustomPalletID from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['ToBinName'])."'";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row6 = mysqli_fetch_assoc($q6);
					$data['ToCustomPalletID'] = $row6['CustomPalletID'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid To BIN';
					return json_encode($json);
				}
				//End get to custompallet details

				$query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q);
					if($to_cp['StatusID'] != '1') {
						$json['Success'] = false;
						$json['Result'] = 'To BIN Status is not Active';
						return json_encode($json);
					}

					if($to_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
						$json['Success'] = false;
						$json['Result'] = 'To BIN Facility is different from Users Facility';
						return json_encode($json);
					}

					// Validate destination bin capacity (only if MaxLimitRequired = 1)
					if($to_cp['MaxLimitRequired'] == '1') {
						$currentAssetsInToBin = intval($to_cp['AssetsCount']);
						$maxAssetsAllowed = intval($to_cp['MaximumAssets']);
						$assetsToMove = intval($from_cp['AssetsCount']);

						// Check if destination bin has enough space
						if(($currentAssetsInToBin + $assetsToMove) > $maxAssetsAllowed) {
							$availableSpace = $maxAssetsAllowed - $currentAssetsInToBin;
							$json['Success'] = false;
							$json['Result'] = "Destination bin does not have enough space. Available space: $availableSpace, Assets to move: $assetsToMove. Maximum capacity: $maxAssetsAllowed";
							return json_encode($json);
						}
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid To BIN";
					return json_encode($json);
				}
				//End check CustomPallet Details

				//Start check IF BIN Dispositions matches
				if($to_cp['AcceptAllDisposition'] == 0) {
					// Check if ToBin has no disposition (null or empty)
					if(empty($to_cp['disposition_id']) || $to_cp['disposition_id'] == '0') {
						// ToBin has no disposition, automatically assign FromBin's disposition
						if(!empty($from_cp['disposition_id']) && $from_cp['disposition_id'] != '0') {
							// Update ToBin with FromBin's disposition
							$updateDispositionQuery = "UPDATE custompallet SET disposition_id = '".mysqli_real_escape_string($this->connectionlink,$from_cp['disposition_id'])."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
							$updateDispositionResult = mysqli_query($this->connectionlink, $updateDispositionQuery);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = 'Error updating ToBin disposition: ' . mysqli_error($this->connectionlink);
								return json_encode($json);
							}

							// Get disposition name for tracking
							$dispositionName = '';
							$dispositionQuery = "SELECT disposition FROM disposition WHERE disposition_id = '".mysqli_real_escape_string($this->connectionlink,$from_cp['disposition_id'])."'";
							$dispositionResult = mysqli_query($this->connectionlink, $dispositionQuery);
							if(mysqli_num_rows($dispositionResult) > 0) {
								$dispositionRow = mysqli_fetch_assoc($dispositionResult);
								$dispositionName = $dispositionRow['disposition'];
							}

							// Add tracking record for disposition assignment
							$trackingAction = "Bin disposition automatically assigned to '" .
											(!empty($dispositionName) ? $dispositionName : "Disposition ID: " . $from_cp['disposition_id']) .
											"' during consolidation from " . $from_cp['BinName'];
							$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
											  VALUES ('".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',
													  '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',
													  '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."',
													  NOW(),
													  '".$_SESSION['user']['UserId']."',
													  '".mysqli_real_escape_string($this->connectionlink,$moduleName)."')";
							mysqli_query($this->connectionlink, $trackingQuery);
							if(mysqli_error($this->connectionlink)) {
								// Log error but don't fail the main operation
								error_log("Error adding disposition tracking record: " . mysqli_error($this->connectionlink));
							}

							// Update the local $to_cp array to reflect the change for subsequent validations
							$to_cp['disposition_id'] = $from_cp['disposition_id'];
						}
					} else {
						// ToBin has a disposition, validate they match
						if($to_cp['disposition_id'] != $from_cp['disposition_id']) {
							$json['Success'] = false;
							$json['Result'] = "Dispositions of the BINs are different and 'Accept All Disposition' is not enabled for To BIN";
							return json_encode($json);
						}
					}
				}
				//End check IF BIN Dispositions matches

				//Start Customer Lock Validation
				if($to_cp['CustomerLock'] == '1') {
					// Get destination bin's customer
					$destinationCustomerID = $to_cp['AWSCustomerID'];

					// Check if destination bin is empty (no customer assigned yet)
					if($destinationCustomerID == NULL || $destinationCustomerID == '' || $destinationCustomerID == '0') {
						// Destination bin is empty, get customer from source records
						$sourceCustomerQuery = "
							SELECT DISTINCT AWSCustomerID FROM shipping_container_serials
							WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'
							AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
						";
						$sourceCustomerResult = mysqli_query($this->connectionlink, $sourceCustomerQuery);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						$sourceCustomers = array();
						while($row = mysqli_fetch_assoc($sourceCustomerResult)) {
							$sourceCustomers[] = $row['AWSCustomerID'];
						}

						// Check if all source records belong to the same customer
						if(count($sourceCustomers) > 1) {
							// Get customer names for error message
							$customerNames = array();
							foreach($sourceCustomers as $custId) {
								$custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$custId)."'";
								$custResult = mysqli_query($this->connectionlink, $custQuery);
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$custRow = mysqli_fetch_assoc($custResult);
									$customerNames[] = $custRow['Customer'];
								}
							}
							$json['Success'] = false;
							$json['Result'] = 'Source bin contains records from multiple customers (' . implode(', ', $customerNames) . '). Cannot consolidate to customer-locked bin.';
							return json_encode($json);
						}

						if(count($sourceCustomers) == 1) {
							// All source records belong to same customer, destination bin will be assigned to this customer
							$sourceCustomerID = $sourceCustomers[0];
							$custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$sourceCustomerID)."'";
							$custResult = mysqli_query($this->connectionlink, $custQuery);
							$customerName = 'Unknown Customer';
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$custRow = mysqli_fetch_assoc($custResult);
								$customerName = $custRow['Customer'];
							}

							// Update destination bin's customer
							$updateBinQuery = "UPDATE custompallet SET AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$sourceCustomerID)."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
							$updateBinResult = mysqli_query($this->connectionlink, $updateBinQuery);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}

							// Add tracking record
							$trackingAction = "Bin customer assigned to " . $customerName . " during consolidation from " . $from_cp['BinName'];
							$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName) VALUES ('".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."', '".mysqli_real_escape_string($this->connectionlink,$moduleName)."')";
							mysqli_query($this->connectionlink, $trackingQuery);
						}
					} else {
						// Destination bin has a customer, validate all source records match
						$mismatchQuery = "
							SELECT COUNT(*) as mismatch_count FROM shipping_container_serials
							WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'
							AND (AWSCustomerID IS NULL OR AWSCustomerID = '' OR AWSCustomerID = '0' OR AWSCustomerID != '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."')
						";
						$mismatchResult = mysqli_query($this->connectionlink, $mismatchQuery);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						$mismatchRow = mysqli_fetch_assoc($mismatchResult);
						if($mismatchRow['mismatch_count'] > 0) {
							// Get customer names for error message
							$destCustQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."'";
							$destCustResult = mysqli_query($this->connectionlink, $destCustQuery);
							$destCustomerName = 'Unknown Customer';
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$destCustRow = mysqli_fetch_assoc($destCustResult);
								$destCustomerName = $destCustRow['Customer'];
							}

							$json['Success'] = false;
							$json['Result'] = 'Destination bin is locked to ' . $destCustomerName . ', but source bin contains records from different customers. Cannot consolidate.';
							return json_encode($json);
						}
					}
				}
				//End Customer Lock Validation

				//Start consolidating BINs

				//Start update shipping_container_serials (ProcessPickPath uses this table instead of asset)
				$query1 = "update shipping_container_serials set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',BinName = '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update shipping_container_serials

				//Insert into Asset Tracking for shipping_container_serials
				$desc = "Asset Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in ProcessPickPath Screen";
				$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)
				select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' AND AssetScanID IS NOT NULL";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Inserting into Asset Tracking

				//Start insert into BIN movement table for shipping_container_serials
				$event_id = rand(1000000000, 9999999999);
				$batch_event_flag = 'Y';
				$query23 = "insert into bin_consolidation_asset_movement (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,event_id,batch_event_flag)
				select AssetScanID,SerialNumber,'".$from_cp['CustomPalletID']."','".$to_cp['CustomPalletID']."','".$_SESSION['user']['FacilityID']."',UniversalModelNumber,part_type,NOW(),'".$_SESSION['user']['UserId']."','ProcessPickPath','".$from_cp['BinName']."','".$to_cp['BinName']."','".$event_id."','".$batch_event_flag."' from shipping_container_serials where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'
				";
				$q23 = mysqli_query($this->connectionlink,$query23);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert into BIN movement table

				//Insert into SPEED SERVER Tracking for shipping_container_serials with ServerID
				$desc = "Serial Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in ProcessPickPath Screen";
				$query3 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,Action,Description,CreatedDate,CreatedBy)
				select ServerID,ServerSerialNumber,part_type,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' AND ServerID IS NOT NULL";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					// Log error but don't fail the main operation
					error_log("Error adding server tracking record during consolidation: " . mysqli_error($this->connectionlink));
				}
				//End Inserting into SPEED SERVER Tracking

				//Insert into SPEED Media Tracking for shipping_container_serials with MediaID
				$desc = "Serial Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in ProcessPickPath Screen";
				$query3 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,Action,Description,CreatedDate,CreatedBy)
				select MediaID,MediaSerialNumber,part_type,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' AND MediaID IS NOT NULL";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					// Log error but don't fail the main operation
					error_log("Error adding media tracking record during consolidation: " . mysqli_error($this->connectionlink));
				}
				//End Inserting into SPEED Media Tracking

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items

				//Start update Custom Pallet Counts
				$query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
				$q33 = mysqli_query($this->connectionlink,$query33);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row33 = mysqli_fetch_assoc($q33);
					$query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				// Remove from pick configuration mapping since bin is being consolidated
				if (isset($data['DetailID']) && !empty($data['DetailID'])) {
					$detailID = mysqli_real_escape_string($this->connectionlink, $data['DetailID']);
					$userId = $_SESSION['user']['UserId'];
					$removeFromMappingQuery = "DELETE FROM pick_configuration_details_custompallet_mapping WHERE CustomPalletID = '$from_CustomPalletID' AND DetailID = '$detailID' AND UserID = '$userId'";
					mysqli_query($this->connectionlink, $removeFromMappingQuery);
					if (mysqli_error($this->connectionlink)) {
						// Log error but don't fail the main operation
						error_log("Error removing bin from pick configuration mapping during consolidation: " . mysqli_error($this->connectionlink));
					}
				}

				// Add tracking record for consolidation
				$trackingAction = "Bin consolidated from " . $from_cp['BinName'] . " to " . $to_cp['BinName'] . " in ProcessPickPath module";
				$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
								  VALUES ('".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',
										  '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."',
										  '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."',
										  NOW(),
										  '".$_SESSION['user']['UserId']."',
										  '".mysqli_real_escape_string($this->connectionlink,$moduleName)."')";
				mysqli_query($this->connectionlink, $trackingQuery);

				// Add tracking record for source bin
				$sourceTrackingAction = "Bin emptied during consolidation to " . $to_cp['BinName'] . " in ProcessPickPath module";
				$sourceTrackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
										VALUES ('".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."',
												'".mysqli_real_escape_string($this->connectionlink,$from_cp['BinName'])."',
												'".mysqli_real_escape_string($this->connectionlink,$sourceTrackingAction)."',
												NOW(),
												'".$_SESSION['user']['UserId']."',
												'".mysqli_real_escape_string($this->connectionlink,$moduleName)."')";
				mysqli_query($this->connectionlink, $sourceTrackingQuery);

				//End consolidating BINs

			} // End Bin to Bin Consolidation

			$json['Success'] = true;
			$json['Result'] = 'Bin consolidated successfully';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function NestToBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No data'
		);

		try {
			// Basic validation
			if (!isset($data['CustomPalletID'])) {
				$json['Success'] = false;
				$json['Result'] = 'CustomPalletID is required';
				return json_encode($json);
			}

			$customPalletID = mysqli_real_escape_string($this->connectionlink, $data['CustomPalletID']);
			$newParentBin = isset($data['parentBin']) ? mysqli_real_escape_string($this->connectionlink, $data['parentBin']) : '';

			// For ProcessPickPath, parent bin is required for nesting operation
			if (empty($newParentBin)) {
				$json['Success'] = false;
				$json['Result'] = 'Parent bin name is required for nesting operation';
				return json_encode($json);
			}

			// Get current bin details
			$query = "SELECT cp.*, l.LocationName, lg.GroupName
					  FROM custompallet cp
					  LEFT JOIN location l ON cp.LocationID = l.LocationID
					  LEFT JOIN location_group lg ON cp.GroupID = lg.GroupID
					  WHERE cp.CustomPalletID = '$customPalletID'";
			$result = mysqli_query($this->connectionlink, $query);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($result) == 0) {
				$json['Success'] = false;
				$json['Result'] = "Bin not found";
				return json_encode($json);
			}

			$row = mysqli_fetch_assoc($result);

			// Validation checks
			if($row['StatusID'] != '1') {
				$json['Success'] = false;
				$json['Result'] = "Bin Status is not active";
				return json_encode($json);
			}

			$changes = array();

			// Check if trying to set bin as its own parent
			if (!empty($newParentBin) && $newParentBin == $row['BinName']) {
				$json['Success'] = false;
				$json['Result'] = "A bin cannot be its own parent";
				return json_encode($json);
			}

			// Check if parent bin is the same as current
			if (!empty($newParentBin) && $newParentBin == $row['ParentBinName']) {
				$json['Success'] = true;
				$json['Result'] = "Parent bin is already set to: " . $newParentBin;
				return json_encode($json);
			}

			// Handle parent bin update
			if (!empty($newParentBin)) {
				// Get parent bin details (fixed to include AssetsCount that recovery module was missing)
				$parentQuery = "SELECT CustomPalletID, BinName, disposition_id, FacilityID, AssetsCount
							   FROM custompallet
							   WHERE BinName = '$newParentBin' AND StatusID = 1";
				$parentResult = mysqli_query($this->connectionlink, $parentQuery);

				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($parentResult) == 0) {
					$json['Success'] = false;
					$json['Result'] = "Parent bin not found or inactive";
					return json_encode($json);
				}

				$parentRow = mysqli_fetch_assoc($parentResult);

				// Validate facility match
				if ($parentRow['FacilityID'] != $row['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = "Parent bin must be in the same facility";
					return json_encode($json);
				}

				// Validate disposition compatibility
				$parentDisposition = $parentRow['disposition_id'];
				$parentAssetsCount = intval($parentRow['AssetsCount']);
				$childDisposition = $row['disposition_id'];

				// Check if parent bin is empty and has no disposition assigned
				if ($parentAssetsCount == 0 && (empty($parentDisposition) || $parentDisposition == '0' || is_null($parentDisposition))) {
					// Parent bin is empty and has no disposition, assign child's disposition to parent
					if (!empty($childDisposition) && $childDisposition != '0') {
						$updateParentDispositionQuery = "UPDATE custompallet SET
							disposition_id = '$childDisposition',
							LastModifiedDate = NOW(),
							LastModifiedBy = '".$_SESSION['user']['UserId']."'
							WHERE CustomPalletID = '".$parentRow['CustomPalletID']."'";

						mysqli_query($this->connectionlink, $updateParentDispositionQuery);
						if (mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = 'Failed to update parent bin disposition: ' . mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						// Get disposition name for tracking
						$dispositionQuery = "SELECT disposition FROM disposition WHERE disposition_id = '$childDisposition'";
						$dispositionResult = mysqli_query($this->connectionlink, $dispositionQuery);
						$dispositionName = '';
						if ($dispositionResult && mysqli_num_rows($dispositionResult) > 0) {
							$dispositionData = mysqli_fetch_assoc($dispositionResult);
							$dispositionName = $dispositionData['disposition'];
						}

						$changes[] = "Parent bin disposition updated to: " . $dispositionName;

						// Add tracking record for parent bin disposition change
						$parentTrackingAction = "Disposition automatically assigned from child bin during nesting - Disposition: " . $dispositionName;
						$parentTrackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
												VALUES ('".$parentRow['CustomPalletID']."', '".mysqli_real_escape_string($this->connectionlink, $parentRow['BinName'])."', '".mysqli_real_escape_string($this->connectionlink, $parentTrackingAction)."', NOW(), '".$_SESSION['user']['UserId']."', 'ProcessPickPath')";
						mysqli_query($this->connectionlink, $parentTrackingQuery);

						// Update parent disposition for further validation
						$parentDisposition = $childDisposition;
					}
				}

				// Now validate disposition compatibility
				if ($parentDisposition != $childDisposition) {
					$json['Success'] = false;
					$json['Result'] = "Parent and child bins must have the same disposition";
					return json_encode($json);
				}

				// Check if bin is currently in a location group - need to remove it
				if (!empty($row['GroupID']) || !empty($row['LocationID'])) {
					// Unlock the current location
					if (!empty($row['LocationID'])) {
						$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$row['LocationID']."'";
						mysqli_query($this->connectionlink, $sqllocold);
						if (mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						$changes[] = "Removed from location: " . $row['LocationName'];
					}

					if (!empty($row['GroupID'])) {
						$changes[] = "Removed from location group: " . $row['GroupName'];
					}
				}

				// Remove bin from pick configuration mapping since it's becoming a child bin
				$removeFromMappingQuery = "DELETE FROM pick_configuration_details_custompallet_mapping WHERE CustomPalletID = '$customPalletID'";
				mysqli_query($this->connectionlink, $removeFromMappingQuery);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Failed to remove bin from pick configuration mapping: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Check if bin was removed from pick configuration mapping
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$changes[] = "Removed from pick configuration mapping";
				}

				// Update bin with new parent
				$updateQuery = "UPDATE custompallet SET
							   ParentCustomPalletID = '".$parentRow['CustomPalletID']."',
							   ParentBinName = '$newParentBin',
							   GroupID = NULL,
							   LocationID = NULL,
							   LastModifiedDate = NOW(),
							   LastModifiedBy = '".$_SESSION['user']['UserId']."'
							   WHERE CustomPalletID = '$customPalletID'";

				mysqli_query($this->connectionlink, $updateQuery);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$changes[] = "Parent bin changed to: " . $newParentBin;
			}

			// Record tracking if there were changes
			if (!empty($changes)) {
				$changeDescription = "Bin nested to parent from ProcessPickPath - " . implode(", ", $changes);

				// Insert tracking record
				$userId = $_SESSION['user']['UserId'];
				$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
								 VALUES ('$customPalletID', '".$row['BinName']."', '".mysqli_real_escape_string($this->connectionlink, $changeDescription)."', NOW(), '$userId', 'ProcessPickPath')";
				mysqli_query($this->connectionlink, $trackingQuery);

				$json['Success'] = true;
				$json['Result'] = "Bin nested to parent successfully";
			} else {
				$json['Success'] = true;
				$json['Result'] = "No changes detected";
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
		}

		return json_encode($json);
	}
}



?>